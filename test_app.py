#!/usr/bin/env python3
"""
Simple test script for the CodeCraft AI Terminal
"""

try:
    print("Starting CodeCraft AI Terminal test...")
    from codecraft.main import CodeCraftTerminal
    print("Import successful")
    
    terminal = CodeCraftTerminal()
    print("Terminal created successfully")
    
    # Test the UI components
    terminal.ui.print_header()
    terminal.ui.print_welcome_message()
    terminal.ui.print_help()
    
    print("UI test completed successfully")
    
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
