#!/usr/bin/env python3
"""
CraftCode v1.6 - Main application
"""

import sys
import os
import signal
from typing import Optional
from prompt_toolkit import prompt
from prompt_toolkit.shortcuts import confirm
from prompt_toolkit.formatted_text import HTML
from prompt_toolkit.styles import Style

try:
    from .ui import <PERSON><PERSON>
    from .api_client import OpenRouterClient
    from .token_manager import TokenManager
    from .conversation import ConversationManager
    from .slash_commands import SlashCommandManager
    from .models import ModelManager
except ImportError:
    # For standalone executable
    from ui import <PERSON><PERSON>
    from api_client import OpenRouterClient
    from token_manager import TokenManager
    from conversation import ConversationManager
    from slash_commands import SlashCommandManager
    from models import ModelManager


class CraftCodeTerminal:
    """Main terminal application class for CraftCode v1.6"""

    def __init__(self):
        self.ui = <PERSON><PERSON>()
        self.api_client = OpenRouterClient()
        self.token_manager = TokenManager()
        self.conversation_manager = ConversationManager()
        self.model_manager = ModelManager()
        self.slash_commands = SlashCommandManager(self)
        self.running = True

        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.ui.print_warning("Shutting down gracefully...")
        self.shutdown()
    
    def start(self):
        """Start the terminal application"""
        try:
            self.ui.clear_screen()
            self.ui.print_header()
            self.ui.print_welcome_message()

            # Show current model info
            current_model = self.model_manager.get_model_display_name()
            self.ui.console.print(f"[bold]Current AI Model:[/bold] {current_model}")
            self.ui.console.print("[muted]Use '/models' to see all available models or '/switch <model>' to change[/muted]")
            self.ui.console.print()
            
            while self.running:
                try:
                    # Get user input
                    user_input = self._get_user_input()
                    
                    if not user_input.strip():
                        continue
                    
                    # Handle special case: just "/" shows the menu
                    if user_input.strip() == "/":
                        self.ui.show_slash_command_menu(self.model_manager)
                        continue

                    # Handle slash commands first
                    if self.slash_commands.is_slash_command(user_input):
                        self.slash_commands.execute_command(user_input)
                        continue

                    # Handle traditional commands
                    if self._handle_command(user_input.strip()):
                        continue
                    
                    # Send to AI
                    self._handle_ai_interaction(user_input)
                    
                except KeyboardInterrupt:
                    self.ui.print_warning("Use 'exit' or 'quit' to close the application.")
                    continue
                except EOFError:
                    break
                except Exception as e:
                    self.ui.print_error(f"Unexpected error: {str(e)}")
                    continue
        
        finally:
            self.shutdown()
    
    def _get_user_input(self) -> str:
        """Get user input with custom prompt"""
        # Estimate tokens for current conversation
        conversation_history = self.conversation_manager.get_api_format_history()
        
        # Create custom prompt style
        style = Style.from_dict({
            'prompt': '#FF6B35 bold',
            'tokens': '#9B9B9B',
        })
        
        # Show session stats in prompt
        session_stats = self.token_manager.get_session_summary()
        if session_stats['message_count'] > 0:
            prompt_text = HTML(
                f'<prompt>You</prompt> '
                f'<tokens>[{self.token_manager.format_tokens(session_stats["total_tokens"])} tokens, '
                f'{self.token_manager.format_cost(session_stats["total_cost"])}]</tokens> '
                f'<prompt>></prompt> '
            )
        else:
            prompt_text = HTML('<prompt>You</prompt> <prompt>></prompt> ')
        
        return prompt(prompt_text, style=style)
    
    def _handle_command(self, user_input: str) -> bool:
        """Handle built-in commands"""
        command = user_input.lower()
        
        if command in ['exit', 'quit']:
            self.running = False
            return True
        
        elif command == 'help':
            self.ui.print_help()
            return True
        
        elif command == 'clear':
            self.ui.clear_screen()
            return True
        
        elif command == 'history':
            self._show_conversation_history()
            return True
        
        elif command == 'stats':
            self._show_statistics()
            return True
        
        elif command == 'save':
            self._save_conversation()
            return True
        
        elif command == 'load':
            self._load_conversation()
            return True
        
        elif command == 'reset':
            self._reset_conversation()
            return True
        
        elif command.startswith('export '):
            format_type = command.split(' ', 1)[1] if ' ' in command else 'txt'
            self._export_conversation(format_type)
            return True
        
        return False
    
    def _handle_ai_interaction(self, user_input: str):
        """Handle AI interaction"""
        try:
            # Show thinking animation
            progress = self.ui.show_thinking()
            
            try:
                # Send message to AI
                response = self.api_client.send_message(user_input)
                
                # Stop thinking animation
                progress.stop()
                
                # Calculate fake cost based on current model
                fake_cost = self.model_manager.calculate_fake_cost(
                    response.input_tokens,
                    response.output_tokens
                )

                # Add to conversation history (use fake cost for display)
                self.conversation_manager.add_message("user", user_input, response.input_tokens, 0)
                self.conversation_manager.add_message("assistant", response.content, 0, fake_cost)

                # Update token manager (use real cost for tracking)
                self.token_manager.add_interaction(
                    response.input_tokens,
                    response.output_tokens,
                    response.cost,  # Real cost for internal tracking
                    user_input
                )

                # Display response with fake cost
                self.ui.print_ai_response(
                    response.content,
                    response.input_tokens,
                    response.output_tokens,
                    fake_cost,  # Show fake cost to user
                    use_status_indicators=True
                )
                
            except Exception as e:
                progress.stop()
                raise e
                
        except Exception as e:
            self.ui.print_error(str(e), self._get_error_suggestion(str(e)))
    
    def _get_error_suggestion(self, error_message: str) -> Optional[str]:
        """Get suggestion for error message"""
        error_lower = error_message.lower()
        
        if "network" in error_lower or "connection" in error_lower:
            return "Check your internet connection and try again"
        elif "rate limit" in error_lower:
            return "Wait a moment before sending another message"
        elif "authentication" in error_lower:
            return "The API key may be invalid or expired"
        elif "timeout" in error_lower:
            return "Try again with a shorter message"
        
        return None
    
    def _show_conversation_history(self, count: int = None):
        """Show conversation history with enhanced formatting"""
        history = self.conversation_manager.get_conversation_history()

        if not history:
            self.ui.print_info("No conversation history yet.")
            return

        # Limit to specified count if provided
        if count:
            history = history[-count:]

        self.ui.console.print(f"\n[bold]📝 Conversation History[/bold] ({len(history)} messages)")
        self.ui.console.print("─" * 100, style="border")

        total_tokens = 0
        total_cost = 0.0

        for i, msg in enumerate(history, 1):
            timestamp = msg.timestamp.split('T')[1][:8] if 'T' in msg.timestamp else msg.timestamp
            role_style = "primary" if msg.role == "user" else "success"
            role_name = "You" if msg.role == "user" else "Claude"

            # Show message preview with token info
            preview = msg.content[:80] + "..." if len(msg.content) > 80 else msg.content

            # Token and cost info
            tokens_info = ""
            if msg.tokens:
                total_tokens += msg.tokens
                tokens_info = f" [{msg.tokens} tokens"
                if msg.cost:
                    total_cost += msg.cost
                    tokens_info += f", ${msg.cost:.6f}"
                tokens_info += "]"

            self.ui.console.print(
                f"[{role_style}]{i:2d}. {role_name:6s}[/{role_style}] "
                f"[muted][{timestamp}][/muted] {preview}"
                f"[muted]{tokens_info}[/muted]"
            )

        self.ui.console.print("─" * 100, style="border")
        if total_tokens > 0:
            self.ui.console.print(
                f"[muted]Total: {total_tokens} tokens, ${total_cost:.6f}[/muted]"
            )
        self.ui.console.print()
    
    def _show_statistics(self, detailed: bool = False):
        """Show token usage and cost statistics with enhanced details"""
        session_stats = self.token_manager.get_session_summary()
        total_usage = self.token_manager.load_total_usage()

        # Create main statistics table
        from rich.table import Table
        from rich.columns import Columns

        table = Table(title="📊 Usage Statistics", border_style="border")
        table.add_column("Metric", style="primary", width=20)
        table.add_column("Current Session", style="text", width=15)
        table.add_column("All Time", style="muted", width=15)

        table.add_row(
            "Messages",
            str(session_stats["message_count"]),
            f"{total_usage['total_sessions']} sessions"
        )
        table.add_row(
            "Input Tokens",
            self.token_manager.format_tokens(session_stats["total_input_tokens"]),
            "-"
        )
        table.add_row(
            "Output Tokens",
            self.token_manager.format_tokens(session_stats["total_output_tokens"]),
            "-"
        )
        table.add_row(
            "Total Tokens",
            self.token_manager.format_tokens(session_stats["total_tokens"]),
            self.token_manager.format_tokens(total_usage["total_tokens"])
        )
        table.add_row(
            "Total Cost",
            self.token_manager.format_cost(session_stats["total_cost"]),
            self.token_manager.format_cost(total_usage["total_cost"])
        )

        if session_stats["message_count"] > 0:
            table.add_row(
                "Avg Cost/Message",
                self.token_manager.format_cost(session_stats["average_cost_per_message"]),
                "-"
            )
            table.add_row(
                "Avg Tokens/Message",
                f"{session_stats['average_tokens_per_message']:.1f}",
                "-"
            )

        self.ui.console.print(table)

        if detailed and session_stats["message_count"] > 0:
            # Show recent interactions
            recent = self.token_manager.get_recent_interactions(5)
            if recent:
                self.ui.console.print("\n[bold]📈 Recent Interactions[/bold]")
                detail_table = Table(border_style="border", show_header=True)
                detail_table.add_column("Time", style="muted", width=8)
                detail_table.add_column("Input", style="warning", width=8)
                detail_table.add_column("Output", style="success", width=8)
                detail_table.add_column("Cost", style="primary", width=10)
                detail_table.add_column("Message", style="text", width=40)

                for interaction in recent:
                    time_str = interaction.timestamp.split('T')[1][:5] if 'T' in interaction.timestamp else interaction.timestamp[:5]
                    detail_table.add_row(
                        time_str,
                        str(interaction.input_tokens),
                        str(interaction.output_tokens),
                        f"${interaction.cost:.6f}",
                        interaction.message_preview
                    )

                self.ui.console.print(detail_table)

        self.ui.console.print()
    
    def _save_conversation(self):
        """Save current conversation"""
        try:
            if not self.conversation_manager.current_conversation:
                self.ui.print_warning("No conversation to save.")
                return
            
            filepath = self.conversation_manager.save_conversation()
            self.ui.print_success(f"Conversation saved to: {os.path.basename(filepath)}")
            
        except Exception as e:
            self.ui.print_error(f"Failed to save conversation: {str(e)}")
    
    def _load_conversation(self):
        """Load a saved conversation"""
        try:
            conversations = self.conversation_manager.list_saved_conversations()
            
            if not conversations:
                self.ui.print_info("No saved conversations found.")
                return
            
            # Show available conversations
            self.ui.console.print("\n[bold]📁 Saved Conversations[/bold]")
            for i, conv in enumerate(conversations[:10], 1):  # Show last 10
                self.ui.console.print(
                    f"{i}. {conv['title']} "
                    f"[muted]({conv['message_count']} messages, {conv['created'][:10]})[/muted]"
                )
            
            self.ui.console.print("\n[muted]Enter filename to load (or press Enter to cancel):[/muted]")
            
        except Exception as e:
            self.ui.print_error(f"Failed to list conversations: {str(e)}")
    
    def _reset_conversation(self):
        """Reset current conversation"""
        if self.conversation_manager.current_conversation:
            if confirm("Are you sure you want to clear the conversation history?"):
                self.conversation_manager.clear_conversation()
                self.api_client.clear_history()
                self.token_manager.reset_session()
                self.ui.print_success("Conversation history cleared.")
        else:
            self.ui.print_info("No conversation to reset.")
    
    def _export_conversation(self, format_type: str, filename: str = None):
        """Export current conversation with enhanced options"""
        try:
            if not self.conversation_manager.current_conversation:
                self.ui.print_warning("No conversation to export.")
                return

            # Support additional formats
            if format_type not in ['txt', 'md', 'json', 'csv']:
                self.ui.print_error(f"Unsupported format: {format_type}")
                self.ui.console.print("Supported formats: txt, md, json, csv")
                return

            if format_type == 'csv':
                filepath = self._export_to_csv(filename)
            elif format_type == 'json':
                filepath = self._export_to_json(filename)
            else:
                filepath = self.conversation_manager.export_conversation(format_type)

            self.ui.print_success(f"Conversation exported to: {os.path.basename(filepath)}")

        except Exception as e:
            self.ui.print_error(f"Failed to export conversation: {str(e)}")

    def _export_to_csv(self, filename: str = None) -> str:
        """Export conversation to CSV format"""
        import csv
        from datetime import datetime

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"conversation_{timestamp}.csv"

        filepath = os.path.join(self.conversation_manager.conversations_dir, filename)

        with open(filepath, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)
            writer.writerow(['Timestamp', 'Role', 'Content', 'Tokens', 'Cost'])

            for msg in self.conversation_manager.current_conversation:
                writer.writerow([
                    msg.timestamp,
                    msg.role,
                    msg.content.replace('\n', ' '),
                    msg.tokens or 0,
                    msg.cost or 0.0
                ])

        return filepath

    def _export_to_json(self, filename: str = None) -> str:
        """Export conversation to JSON format"""
        import json
        from datetime import datetime

        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"conversation_{timestamp}.json"

        filepath = os.path.join(self.conversation_manager.conversations_dir, filename)

        data = {
            "metadata": {
                "exported": datetime.now().isoformat(),
                "version": "1.6.0",
                "message_count": len(self.conversation_manager.current_conversation)
            },
            "messages": [
                {
                    "timestamp": msg.timestamp,
                    "role": msg.role,
                    "content": msg.content,
                    "tokens": msg.tokens,
                    "cost": msg.cost
                }
                for msg in self.conversation_manager.current_conversation
            ]
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        return filepath

    def _search_conversations(self, query: str):
        """Search conversations with enhanced results"""
        try:
            # Search current conversation
            current_results = []
            for i, msg in enumerate(self.conversation_manager.current_conversation):
                if query.lower() in msg.content.lower():
                    current_results.append((i, msg))

            # Search saved conversations
            saved_results = self.conversation_manager.search_conversations(query)

            if not current_results and not saved_results:
                self.ui.print_info(f"No results found for '{query}'")
                return

            self.ui.console.print(f"\n[bold]🔍 Search Results for '{query}'[/bold]")

            if current_results:
                self.ui.console.print("\n[bold secondary]Current Conversation:[/bold secondary]")
                for i, msg in current_results[:5]:  # Limit to 5 results
                    preview = self._get_search_preview(msg.content, query)
                    role = "You" if msg.role == "user" else "Claude"
                    self.ui.console.print(f"  {i+1}. [{role}] {preview}")

            if saved_results:
                self.ui.console.print(f"\n[bold secondary]Saved Conversations ({len(saved_results)} found):[/bold secondary]")
                for result in saved_results[:5]:  # Limit to 5 results
                    self.ui.console.print(f"  📁 {result['title']} - {result['match_preview']}")

            self.ui.console.print()

        except Exception as e:
            self.ui.print_error(f"Search failed: {str(e)}")

    def _get_search_preview(self, content: str, query: str, context: int = 50) -> str:
        """Get search preview with highlighted context"""
        content_lower = content.lower()
        query_lower = query.lower()

        index = content_lower.find(query_lower)
        if index == -1:
            return content[:100] + "..." if len(content) > 100 else content

        start = max(0, index - context)
        end = min(len(content), index + len(query) + context)

        preview = content[start:end]
        if start > 0:
            preview = "..." + preview
        if end < len(content):
            preview = preview + "..."

        return preview

    def _analyze_conversation(self, analysis_type: str):
        """Analyze conversation patterns and provide insights"""
        try:
            history = self.conversation_manager.current_conversation
            if not history:
                self.ui.print_info("No conversation to analyze.")
                return

            self.ui.console.print(f"\n[bold]📊 Conversation Analysis ({analysis_type})[/bold]")

            if analysis_type == "general":
                self._general_analysis(history)
            elif analysis_type == "tokens":
                self._token_analysis(history)
            elif analysis_type == "patterns":
                self._pattern_analysis(history)
            else:
                self.ui.print_warning(f"Unknown analysis type: {analysis_type}")
                self.ui.console.print("Available types: general, tokens, patterns")

        except Exception as e:
            self.ui.print_error(f"Analysis failed: {str(e)}")

    def _general_analysis(self, history):
        """Perform general conversation analysis"""
        user_msgs = [msg for msg in history if msg.role == "user"]
        ai_msgs = [msg for msg in history if msg.role == "assistant"]

        avg_user_length = sum(len(msg.content) for msg in user_msgs) / len(user_msgs) if user_msgs else 0
        avg_ai_length = sum(len(msg.content) for msg in ai_msgs) / len(ai_msgs) if ai_msgs else 0

        # Word frequency analysis
        all_words = []
        for msg in user_msgs:
            all_words.extend(msg.content.lower().split())

        word_freq = {}
        for word in all_words:
            word = word.strip('.,!?";')
            if len(word) > 3:  # Only count words longer than 3 chars
                word_freq[word] = word_freq.get(word, 0) + 1

        top_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:5]

        from rich.table import Table
        table = Table(border_style="border")
        table.add_column("Metric", style="primary")
        table.add_column("Value", style="text")

        table.add_row("Total Messages", str(len(history)))
        table.add_row("User Messages", str(len(user_msgs)))
        table.add_row("AI Messages", str(len(ai_msgs)))
        table.add_row("Avg User Message Length", f"{avg_user_length:.1f} chars")
        table.add_row("Avg AI Message Length", f"{avg_ai_length:.1f} chars")
        table.add_row("Most Used Words", ", ".join([f"{word}({count})" for word, count in top_words]))

        self.ui.console.print(table)
        self.ui.console.print()

    def _token_analysis(self, history):
        """Perform token usage analysis"""
        token_data = [(msg.tokens or 0, msg.cost or 0.0) for msg in history if msg.tokens]

        if not token_data:
            self.ui.print_info("No token data available for analysis.")
            return

        total_tokens = sum(tokens for tokens, _ in token_data)
        total_cost = sum(cost for _, cost in token_data)
        avg_tokens = total_tokens / len(token_data)
        avg_cost = total_cost / len(token_data)

        # Find highest and lowest token usage
        max_tokens = max(tokens for tokens, _ in token_data)
        min_tokens = min(tokens for tokens, _ in token_data if tokens > 0)

        from rich.table import Table
        table = Table(title="Token Usage Analysis", border_style="border")
        table.add_column("Metric", style="primary")
        table.add_column("Value", style="text")

        table.add_row("Total Tokens", self.token_manager.format_tokens(total_tokens))
        table.add_row("Total Cost", self.token_manager.format_cost(total_cost))
        table.add_row("Average Tokens/Message", f"{avg_tokens:.1f}")
        table.add_row("Average Cost/Message", self.token_manager.format_cost(avg_cost))
        table.add_row("Highest Token Usage", str(max_tokens))
        table.add_row("Lowest Token Usage", str(min_tokens))

        self.ui.console.print(table)
        self.ui.console.print()

    def _pattern_analysis(self, history):
        """Analyze conversation patterns"""
        # Time pattern analysis
        timestamps = [msg.timestamp for msg in history]

        # Message length patterns
        user_lengths = [len(msg.content) for msg in history if msg.role == "user"]
        ai_lengths = [len(msg.content) for msg in history if msg.role == "assistant"]

        self.ui.console.print("[bold]📈 Conversation Patterns[/bold]")
        self.ui.console.print(f"• User message lengths: {min(user_lengths) if user_lengths else 0}-{max(user_lengths) if user_lengths else 0} chars")
        self.ui.console.print(f"• AI message lengths: {min(ai_lengths) if ai_lengths else 0}-{max(ai_lengths) if ai_lengths else 0} chars")
        self.ui.console.print(f"• Total conversation span: {len(history)} messages")
        self.ui.console.print()

    def _show_cost_breakdown(self, period: str):
        """Show detailed cost breakdown"""
        session_stats = self.token_manager.get_session_summary()

        self.ui.console.print(f"\n[bold]💰 Cost Breakdown ({period})[/bold]")

        if period == "session":
            if session_stats["message_count"] == 0:
                self.ui.print_info("No usage in current session.")
                return

            from rich.table import Table
            table = Table(border_style="border")
            table.add_column("Component", style="primary")
            table.add_column("Tokens", style="text")
            table.add_column("Rate (per 1M)", style="muted")
            table.add_column("Cost", style="success")

            input_cost = (session_stats["total_input_tokens"] / 1_000_000) * self.token_manager.input_cost_per_million
            output_cost = (session_stats["total_output_tokens"] / 1_000_000) * self.token_manager.output_cost_per_million

            table.add_row(
                "Input Tokens",
                self.token_manager.format_tokens(session_stats["total_input_tokens"]),
                f"${self.token_manager.input_cost_per_million:.2f}",
                self.token_manager.format_cost(input_cost)
            )
            table.add_row(
                "Output Tokens",
                self.token_manager.format_tokens(session_stats["total_output_tokens"]),
                f"${self.token_manager.output_cost_per_million:.2f}",
                self.token_manager.format_cost(output_cost)
            )
            table.add_row(
                "[bold]Total[/bold]",
                f"[bold]{self.token_manager.format_tokens(session_stats['total_tokens'])}[/bold]",
                "-",
                f"[bold]{self.token_manager.format_cost(session_stats['total_cost'])}[/bold]"
            )

            self.ui.console.print(table)
            self.ui.console.print()

    def shutdown(self):
        """Shutdown the application"""
        try:
            # Save session history
            self.token_manager.save_session_history()
            
            # Show goodbye message
            session_stats = self.token_manager.get_session_summary()
            if session_stats['message_count'] > 0:
                self.ui.console.print(
                    f"\n[muted]Session summary: {session_stats['message_count']} messages, "
                    f"{self.token_manager.format_tokens(session_stats['total_tokens'])} tokens, "
                    f"{self.token_manager.format_cost(session_stats['total_cost'])}[/muted]"
                )
            
            self.ui.console.print("\n[primary]👋 Thank you for using CraftCode v1.6![/primary]")
            
        except Exception as e:
            print(f"Error during shutdown: {e}")
        
        finally:
            sys.exit(0)


def main():
    """Main entry point"""
    try:
        terminal = CraftCodeTerminal()
        terminal.start()
    except Exception as e:
        print(f"Failed to start CraftCode v1.6: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
