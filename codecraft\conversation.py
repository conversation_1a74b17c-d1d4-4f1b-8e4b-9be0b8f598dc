"""
Conversation management and history handling
"""

import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict


@dataclass
class Message:
    """A single message in the conversation"""
    role: str  # 'user', 'assistant', or 'system'
    content: str
    timestamp: str
    tokens: Optional[int] = None
    cost: Optional[float] = None


@dataclass
class ConversationMetadata:
    """Metadata for a conversation"""
    title: str
    created: str
    last_modified: str
    message_count: int
    total_tokens: int
    total_cost: float


class ConversationManager:
    """Manages conversation history and persistence"""
    
    def __init__(self):
        self.current_conversation: List[Message] = []
        self.conversations_dir = "conversations"
        self.ensure_conversations_directory()
    
    def ensure_conversations_directory(self):
        """Ensure conversations directory exists"""
        try:
            os.makedirs(self.conversations_dir, exist_ok=True)
        except Exception as e:
            print(f"Warning: Could not create conversations directory: {e}")
    
    def add_message(self, role: str, content: str, tokens: Optional[int] = None, cost: Optional[float] = None):
        """Add a message to the current conversation"""
        message = Message(
            role=role,
            content=content,
            timestamp=datetime.now().isoformat(),
            tokens=tokens,
            cost=cost
        )
        self.current_conversation.append(message)
    
    def get_conversation_history(self) -> List[Message]:
        """Get current conversation history"""
        return self.current_conversation.copy()
    
    def get_api_format_history(self) -> List[Dict[str, str]]:
        """Get conversation history in API format"""
        return [
            {"role": msg.role, "content": msg.content}
            for msg in self.current_conversation
        ]
    
    def clear_conversation(self):
        """Clear current conversation"""
        self.current_conversation = []
    
    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get statistics for current conversation"""
        if not self.current_conversation:
            return {
                "message_count": 0,
                "user_messages": 0,
                "assistant_messages": 0,
                "total_tokens": 0,
                "total_cost": 0.0,
                "total_characters": 0,
                "average_message_length": 0
            }
        
        user_messages = sum(1 for msg in self.current_conversation if msg.role == "user")
        assistant_messages = sum(1 for msg in self.current_conversation if msg.role == "assistant")
        total_tokens = sum(msg.tokens or 0 for msg in self.current_conversation)
        total_cost = sum(msg.cost or 0.0 for msg in self.current_conversation)
        total_characters = sum(len(msg.content) for msg in self.current_conversation)
        
        return {
            "message_count": len(self.current_conversation),
            "user_messages": user_messages,
            "assistant_messages": assistant_messages,
            "total_tokens": total_tokens,
            "total_cost": total_cost,
            "total_characters": total_characters,
            "average_message_length": total_characters // len(self.current_conversation) if self.current_conversation else 0
        }
    
    def save_conversation(self, title: Optional[str] = None) -> str:
        """Save current conversation to file"""
        if not self.current_conversation:
            raise ValueError("No conversation to save")
        
        # Generate title if not provided
        if not title:
            first_user_message = next(
                (msg.content for msg in self.current_conversation if msg.role == "user"),
                "Untitled Conversation"
            )
            title = first_user_message[:50] + "..." if len(first_user_message) > 50 else first_user_message
            # Clean title for filename
            title = "".join(c for c in title if c.isalnum() or c in (' ', '-', '_')).strip()
        
        # Generate filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{title.replace(' ', '_')}.json"
        filepath = os.path.join(self.conversations_dir, filename)
        
        # Calculate metadata
        stats = self.get_conversation_stats()
        metadata = ConversationMetadata(
            title=title,
            created=self.current_conversation[0].timestamp if self.current_conversation else datetime.now().isoformat(),
            last_modified=datetime.now().isoformat(),
            message_count=stats["message_count"],
            total_tokens=stats["total_tokens"],
            total_cost=stats["total_cost"]
        )
        
        # Prepare data for saving
        conversation_data = {
            "metadata": asdict(metadata),
            "messages": [asdict(msg) for msg in self.current_conversation]
        }
        
        # Save to file
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(conversation_data, f, indent=2, ensure_ascii=False)
            return filepath
        except Exception as e:
            raise Exception(f"Failed to save conversation: {e}")
    
    def load_conversation(self, filename: str):
        """Load conversation from file"""
        filepath = os.path.join(self.conversations_dir, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Conversation file not found: {filename}")
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Validate data structure
            if "messages" not in data:
                raise ValueError("Invalid conversation file format")
            
            # Load messages
            self.current_conversation = []
            for msg_data in data["messages"]:
                message = Message(
                    role=msg_data["role"],
                    content=msg_data["content"],
                    timestamp=msg_data["timestamp"],
                    tokens=msg_data.get("tokens"),
                    cost=msg_data.get("cost")
                )
                self.current_conversation.append(message)
            
            return data.get("metadata", {})
            
        except Exception as e:
            raise Exception(f"Failed to load conversation: {e}")
    
    def list_saved_conversations(self) -> List[Dict[str, Any]]:
        """List all saved conversations with metadata"""
        conversations = []
        
        try:
            if not os.path.exists(self.conversations_dir):
                return conversations
            
            for filename in os.listdir(self.conversations_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(self.conversations_dir, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        metadata = data.get("metadata", {})
                        conversations.append({
                            "filename": filename,
                            "title": metadata.get("title", "Untitled"),
                            "created": metadata.get("created", "Unknown"),
                            "message_count": metadata.get("message_count", 0),
                            "total_tokens": metadata.get("total_tokens", 0),
                            "total_cost": metadata.get("total_cost", 0.0)
                        })
                    except Exception:
                        # Skip corrupted files
                        continue
            
            # Sort by creation date (newest first)
            conversations.sort(key=lambda x: x["created"], reverse=True)
            
        except Exception as e:
            print(f"Warning: Could not list conversations: {e}")
        
        return conversations
    
    def delete_conversation(self, filename: str):
        """Delete a saved conversation"""
        filepath = os.path.join(self.conversations_dir, filename)
        
        if not os.path.exists(filepath):
            raise FileNotFoundError(f"Conversation file not found: {filename}")
        
        try:
            os.remove(filepath)
        except Exception as e:
            raise Exception(f"Failed to delete conversation: {e}")
    
    def search_conversations(self, query: str) -> List[Dict[str, Any]]:
        """Search conversations by content"""
        results = []
        query_lower = query.lower()
        
        for conv_info in self.list_saved_conversations():
            try:
                filepath = os.path.join(self.conversations_dir, conv_info["filename"])
                with open(filepath, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Search in messages
                for msg in data.get("messages", []):
                    if query_lower in msg.get("content", "").lower():
                        results.append({
                            **conv_info,
                            "match_preview": self._get_match_preview(msg["content"], query)
                        })
                        break  # Only add each conversation once
                        
            except Exception:
                continue
        
        return results
    
    def _get_match_preview(self, content: str, query: str, context_length: int = 100) -> str:
        """Get a preview of the match with context"""
        content_lower = content.lower()
        query_lower = query.lower()
        
        index = content_lower.find(query_lower)
        if index == -1:
            return content[:context_length] + "..." if len(content) > context_length else content
        
        start = max(0, index - context_length // 2)
        end = min(len(content), index + len(query) + context_length // 2)
        
        preview = content[start:end]
        if start > 0:
            preview = "..." + preview
        if end < len(content):
            preview = preview + "..."
        
        return preview
    
    def export_conversation(self, format_type: str = "txt") -> str:
        """Export current conversation to different formats"""
        if not self.current_conversation:
            raise ValueError("No conversation to export")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format_type == "txt":
            filename = f"conversation_{timestamp}.txt"
            content = self._export_to_text()
        elif format_type == "md":
            filename = f"conversation_{timestamp}.md"
            content = self._export_to_markdown()
        else:
            raise ValueError(f"Unsupported export format: {format_type}")
        
        filepath = os.path.join(self.conversations_dir, filename)
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            return filepath
        except Exception as e:
            raise Exception(f"Failed to export conversation: {e}")
    
    def _export_to_text(self) -> str:
        """Export conversation to plain text"""
        lines = [
            "CodeCraft AI Terminal Conversation",
            "=" * 40,
            f"Exported: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Messages: {len(self.current_conversation)}",
            "",
        ]
        
        for i, msg in enumerate(self.current_conversation, 1):
            timestamp = datetime.fromisoformat(msg.timestamp).strftime('%H:%M:%S')
            role = "You" if msg.role == "user" else "Claude"
            lines.extend([
                f"[{timestamp}] {role}:",
                msg.content,
                ""
            ])
        
        return "\n".join(lines)
    
    def _export_to_markdown(self) -> str:
        """Export conversation to markdown"""
        lines = [
            "# CodeCraft AI Terminal Conversation",
            "",
            f"**Exported:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  ",
            f"**Messages:** {len(self.current_conversation)}",
            "",
        ]
        
        for msg in self.current_conversation:
            timestamp = datetime.fromisoformat(msg.timestamp).strftime('%H:%M:%S')
            role = "**You**" if msg.role == "user" else "**Claude**"
            lines.extend([
                f"## {role} ({timestamp})",
                "",
                msg.content,
                "",
            ])
        
        return "\n".join(lines)
