#!/usr/bin/env python3
"""
CraftCode v1.6 - Cross-platform launcher script
Single command to start the AI terminal application
"""

import sys
import os
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 8):
        print("❌ Error: Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        print("Please upgrade Python and try again.")
        return False
    return True


def check_dependencies():
    """Check if required dependencies are installed"""
    required_packages = [
        'requests', 'colorama', 'rich', 'pygments', 
        'tiktoken', 'prompt_toolkit', 'click'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n🔧 Installing missing packages...")
        
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install', 
                '--upgrade', '--user'
            ] + missing_packages)
            print("✅ Dependencies installed successfully!")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies automatically.")
            print("Please run: pip install -r requirements.txt")
            return False
    
    return True


def find_application_path():
    """Find the CraftCode application path"""
    # Check current directory
    current_dir = Path.cwd()
    
    # Possible locations
    possible_paths = [
        current_dir / "codecraft" / "main.py",
        current_dir / "main.py",
        current_dir / "src" / "main.py",
        current_dir / "craftcode" / "main.py"
    ]
    
    for path in possible_paths:
        if path.exists():
            return path
    
    return None


def launch_application():
    """Launch the CraftCode application"""
    print("🚀 Starting CraftCode v1.6...")
    print("   Powered by OpenRouter API with DeepSeek model")
    print("   CraftCode Team\n")
    
    # Find application
    app_path = find_application_path()
    if not app_path:
        print("❌ Error: Could not find CraftCode application files")
        print("Please ensure you're in the correct directory or the application is properly installed.")
        return False
    
    try:
        # Launch the application
        if app_path.name == "main.py" and "codecraft" in str(app_path):
            # Run as module
            subprocess.run([sys.executable, '-m', 'codecraft.main'], check=True)
        else:
            # Run directly
            subprocess.run([sys.executable, str(app_path)], check=True)
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Error launching application: {e}")
        return False
    except KeyboardInterrupt:
        print("\n👋 CraftCode terminated by user")
        return True


def show_help():
    """Show help information"""
    help_text = """
🚀 CraftCode v1.6 - AI Terminal Application

USAGE:
    python craftcode.py [options]

OPTIONS:
    --help, -h      Show this help message
    --version, -v   Show version information
    --check         Check system requirements
    --install       Install/update dependencies

EXAMPLES:
    python craftcode.py              # Start CraftCode
    python craftcode.py --check      # Check requirements
    python craftcode.py --install    # Install dependencies

SYSTEM REQUIREMENTS:
    - Python 3.8 or higher
    - Internet connection for AI API
    - 100MB RAM minimum
    - 50MB disk space

For more information, visit: https://github.com/craftcode-team/craftcode
"""
    print(help_text)


def show_version():
    """Show version information"""
    print("CraftCode v1.6.0")
    print("Powered by OpenRouter API with DeepSeek model")
    print("CraftCode Team")
    print(f"Python {sys.version}")
    print(f"Platform: {platform.system()} {platform.release()}")


def main():
    """Main launcher function"""
    args = sys.argv[1:]
    
    # Handle command line arguments
    if '--help' in args or '-h' in args:
        show_help()
        return
    
    if '--version' in args or '-v' in args:
        show_version()
        return
    
    if '--check' in args:
        print("🔍 Checking system requirements...")
        python_ok = check_python_version()
        deps_ok = check_dependencies()
        app_path = find_application_path()
        
        print(f"✅ Python version: {'OK' if python_ok else 'FAIL'}")
        print(f"✅ Dependencies: {'OK' if deps_ok else 'FAIL'}")
        print(f"✅ Application files: {'OK' if app_path else 'FAIL'}")
        
        if python_ok and deps_ok and app_path:
            print("\n🎉 System is ready to run CraftCode!")
        else:
            print("\n❌ System requirements not met. Please fix the issues above.")
        return
    
    if '--install' in args:
        print("🔧 Installing/updating dependencies...")
        if check_dependencies():
            print("✅ All dependencies are up to date!")
        return
    
    # Check requirements before launching
    if not check_python_version():
        sys.exit(1)
    
    if not check_dependencies():
        sys.exit(1)
    
    # Launch the application
    success = launch_application()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
