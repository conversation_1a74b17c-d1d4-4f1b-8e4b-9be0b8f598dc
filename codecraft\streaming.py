"""
Real-time streaming and typing animation for CraftCode v1.6
"""

import time
import threading
from typing import Generator, Optional
from rich.live import Live
from rich.text import Text
from rich.panel import Panel
from rich.rule import Rule
from rich.console import Console


class TypingAnimator:
    """Handles real-time typing animation for AI responses"""
    
    def __init__(self, console: Console):
        self.console = console
        self.typing_speed = 0.03  # Seconds between characters
        self.is_typing = False
        self.current_text = ""
        self.live_display = None
        
    def start_typing_animation(self, input_tokens: int, estimated_output_tokens: int, estimated_cost: float):
        """Start the typing animation with header"""
        self.is_typing = True
        self.current_text = ""
        
        # Create header with token info
        header = Text()
        header.append("Claude", style="bold success")
        
        # Enhanced token display
        token_info = Text()
        token_info.append(" [", style="muted")
        token_info.append("↑", style="warning")
        token_info.append(f"{input_tokens}", style="bold warning")
        token_info.append(" ↓", style="success")
        token_info.append("~", style="muted")
        token_info.append(f"{estimated_output_tokens}", style="bold success")
        token_info.append(" tokens, ", style="muted")
        token_info.append(f"~${estimated_cost:.6f}", style="bold primary")
        token_info.append("]", style="muted")
        
        self.console.print()
        self.console.print(Text.assemble(header, token_info))
        self.console.print(Rule(style="border"))
        
        # Start live display for typing animation
        self.live_display = Live(
            self._create_typing_panel(),
            console=self.console,
            refresh_per_second=30,
            auto_refresh=True
        )
        self.live_display.start()
    
    def _create_typing_panel(self) -> Panel:
        """Create the typing panel with current text and cursor"""
        if not self.current_text:
            display_text = Text("▋", style="success blink")  # Blinking cursor
        else:
            display_text = Text(self.current_text, style="text")
            if self.is_typing:
                display_text.append("▋", style="success blink")  # Blinking cursor
        
        return Panel(
            display_text,
            border_style="muted",
            padding=(0, 1),
            expand=False
        )
    
    def type_character(self, char: str):
        """Add a character to the typing animation"""
        if self.is_typing and self.live_display:
            self.current_text += char
            self.live_display.update(self._create_typing_panel())
            time.sleep(self.typing_speed)
    
    def type_text_stream(self, text_generator: Generator[str, None, None]):
        """Type text from a generator stream"""
        for chunk in text_generator:
            if not self.is_typing:
                break
            for char in chunk:
                self.type_character(char)
    
    def finish_typing(self, final_tokens: int, final_cost: float):
        """Finish the typing animation and show final stats"""
        self.is_typing = False
        
        if self.live_display:
            # Show final text without cursor
            final_panel = Panel(
                Text(self.current_text, style="text"),
                border_style="muted",
                padding=(0, 1),
                expand=False
            )
            self.live_display.update(final_panel)
            self.live_display.stop()
            self.live_display = None
        
        # Update header with final token count
        self.console.print(Rule(style="border"))
        
        # Show final token info
        final_info = Text()
        final_info.append("Final: ", style="muted")
        final_info.append(f"{final_tokens} tokens, ", style="success")
        final_info.append(f"${final_cost:.6f}", style="primary")
        
        self.console.print(final_info, style="muted")
        self.console.print()
    
    def stop_typing(self):
        """Stop the typing animation immediately"""
        self.is_typing = False
        if self.live_display:
            self.live_display.stop()
            self.live_display = None


class StreamingResponseHandler:
    """Handles streaming responses from the API"""
    
    def __init__(self):
        self.response_buffer = ""
        self.chunk_size = 3  # Characters per chunk for smooth animation
    
    def simulate_streaming(self, full_response: str) -> Generator[str, None, None]:
        """Simulate streaming by yielding chunks of the response"""
        words = full_response.split()
        current_chunk = ""
        
        for word in words:
            current_chunk += word + " "
            
            # Yield chunk when it reaches desired size or at sentence boundaries
            if (len(current_chunk) >= self.chunk_size * 5 or 
                word.endswith('.') or word.endswith('!') or word.endswith('?')):
                yield current_chunk
                current_chunk = ""
                time.sleep(0.1)  # Small delay between chunks
        
        # Yield any remaining text
        if current_chunk.strip():
            yield current_chunk
    
    def process_code_blocks(self, text: str) -> Generator[str, None, None]:
        """Process text with special handling for code blocks"""
        import re
        
        # Split text into code blocks and regular text
        code_pattern = r'```(\w+)?\n(.*?)\n```'
        parts = re.split(code_pattern, text, flags=re.DOTALL)
        
        i = 0
        while i < len(parts):
            if i + 2 < len(parts) and parts[i + 1]:  # This is a code block
                # Yield text before code block
                if parts[i].strip():
                    yield from self.simulate_streaming(parts[i])
                
                # Yield code block header
                language = parts[i + 1] if parts[i + 1] else 'text'
                yield f"\n```{language}\n"
                time.sleep(0.2)
                
                # Yield code content line by line
                code_lines = parts[i + 2].split('\n')
                for line in code_lines:
                    yield line + '\n'
                    time.sleep(0.1)
                
                yield "```\n"
                time.sleep(0.2)
                
                i += 3
            else:
                # Regular text
                if parts[i].strip():
                    yield from self.simulate_streaming(parts[i])
                i += 1


class EnhancedResponseFormatter:
    """Enhanced response formatter with typing animation support"""
    
    def __init__(self, console: Console):
        self.console = console
        self.animator = TypingAnimator(console)
        self.streaming_handler = StreamingResponseHandler()
    
    def display_response_with_typing(self, response_text: str, input_tokens: int, 
                                   output_tokens: int, cost: float):
        """Display response with typing animation"""
        try:
            # Estimate tokens for animation start
            estimated_output = max(output_tokens, len(response_text) // 4)
            estimated_cost = cost
            
            # Start typing animation
            self.animator.start_typing_animation(input_tokens, estimated_output, estimated_cost)
            
            # Stream the response
            text_stream = self.streaming_handler.process_code_blocks(response_text)
            self.animator.type_text_stream(text_stream)
            
            # Finish with actual stats
            self.animator.finish_typing(output_tokens, cost)
            
        except KeyboardInterrupt:
            self.animator.stop_typing()
            self.console.print("\n[yellow]⚠️  Response interrupted by user[/yellow]")
        except Exception as e:
            self.animator.stop_typing()
            self.console.print(f"\n[red]❌ Animation error: {str(e)}[/red]")
            # Fallback to regular display
            self.console.print(response_text)
    
    def display_response_instant(self, response_text: str, input_tokens: int, 
                               output_tokens: int, cost: float):
        """Display response instantly (fallback method)"""
        # Create header with token info
        header = Text()
        header.append("Claude", style="bold success")
        
        # Enhanced token display
        token_info = Text()
        token_info.append(" [", style="muted")
        token_info.append("↑", style="warning")
        token_info.append(f"{input_tokens}", style="bold warning")
        token_info.append(" ↓", style="success")
        token_info.append(f"{output_tokens}", style="bold success")
        token_info.append(" tokens, ", style="muted")
        token_info.append(f"${cost:.6f}", style="bold primary")
        token_info.append("]", style="muted")
        
        self.console.print()
        self.console.print(Text.assemble(header, token_info))
        self.console.print(Rule(style="border"))
        
        # Display response
        self.console.print(response_text)
        
        self.console.print(Rule(style="border"))
        self.console.print()
