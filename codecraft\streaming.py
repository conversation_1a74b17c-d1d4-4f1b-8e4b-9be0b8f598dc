"""
Real-time streaming and typing animation for CraftCode v1.6
"""

import time
import threading
import sys
from typing import Generator, Optional
from rich.live import Live
from rich.text import Text
from rich.panel import Panel
from rich.rule import Rule
from rich.console import Console


class StatusIndicator:
    """Handles AI status indicators instead of typing animation"""

    def __init__(self, console: Console):
        self.console = console
        self.current_stage = 0
        self.stages = [
            "AI is analyzing...",
            "AI is writing code...",
            "AI is fixing bugs..."
        ]
        self.is_active = False
        self.live_display = None
        self.interrupted = False
        
    def start_status_display(self, input_tokens: int, estimated_output_tokens: int, estimated_cost: float):
        """Start the status indicator display with header"""
        self.is_active = True
        self.current_stage = 0

        # Create header with token info
        header = Text()
        header.append("Claude", style="bold success")

        # Enhanced token display
        token_info = Text()
        token_info.append(" [", style="muted")
        token_info.append("↑", style="warning")
        token_info.append(f"{input_tokens}", style="bold warning")
        token_info.append(" ↓", style="success")
        token_info.append("~", style="muted")
        token_info.append(f"{estimated_output_tokens}", style="bold success")
        token_info.append(" tokens, ", style="muted")
        token_info.append(f"~${estimated_cost:.6f}", style="bold primary")
        token_info.append("]", style="muted")

        self.console.print()
        self.console.print(Text.assemble(header, token_info))
        self.console.print(Rule(style="border"))

        # Start live display for status indicator
        self.live_display = Live(
            self._create_status_panel(),
            console=self.console,
            refresh_per_second=2,
            auto_refresh=True
        )
        self.live_display.start()
    
    def _create_status_panel(self) -> Panel:
        """Create the status panel with current stage indicator"""
        status_text = Text()
        status_text.append("🤖 ", style="primary")
        status_text.append(self.stages[self.current_stage], style="secondary")

        return Panel(
            status_text,
            border_style="muted",
            padding=(0, 1),
            expand=False
        )
    
    def advance_stage(self):
        """Advance to the next status stage"""
        if self.is_active and self.live_display:
            self.current_stage = (self.current_stage + 1) % len(self.stages)
            self.live_display.update(self._create_status_panel())

    def process_response(self, response_text: str):
        """Process the response and advance through stages with interrupt capability"""
        if not self.is_active:
            return

        try:
            # Stage 1: Analyzing (already shown)
            self._interruptible_sleep(1.0)

            # Stage 2: Writing code
            if not self.interrupted:
                self.advance_stage()
                self._interruptible_sleep(1.5)

            # Stage 3: Fixing bugs
            if not self.interrupted:
                self.advance_stage()
                self._interruptible_sleep(1.0)

        except KeyboardInterrupt:
            self.interrupted = True
            raise

    def _interruptible_sleep(self, duration: float):
        """Sleep that can be interrupted by Escape key"""
        if self.interrupted:
            raise KeyboardInterrupt()

        # Sleep in small increments to allow for interruption
        sleep_increment = 0.1
        elapsed = 0.0

        while elapsed < duration and not self.interrupted:
            time.sleep(min(sleep_increment, duration - elapsed))
            elapsed += sleep_increment

            # Check for keyboard interrupt
            if self.interrupted:
                raise KeyboardInterrupt()
    
    def finish_status(self, final_tokens: int, final_cost: float):
        """Finish the status display and show final stats"""
        self.is_active = False

        if self.live_display:
            # Show completion status
            completion_panel = Panel(
                Text("✅ Response generated successfully", style="success"),
                border_style="success",
                padding=(0, 1),
                expand=False
            )
            self.live_display.update(completion_panel)
            time.sleep(0.5)  # Brief pause to show completion
            self.live_display.stop()
            self.live_display = None

        # Update header with final token count
        self.console.print(Rule(style="border"))

        # Show final token info
        final_info = Text()
        final_info.append("Final: ", style="muted")
        final_info.append(f"{final_tokens} tokens, ", style="success")
        final_info.append(f"${final_cost:.6f}", style="primary")

        self.console.print(final_info, style="muted")
        self.console.print()

    def stop_status(self):
        """Stop the status display immediately"""
        self.is_active = False
        self.interrupted = True
        if self.live_display:
            self.live_display.stop()
            self.live_display = None

    def interrupt(self):
        """Interrupt the status display"""
        self.interrupted = True
        self.stop_status()


class StreamingResponseHandler:
    """Handles streaming responses from the API"""
    
    def __init__(self):
        self.response_buffer = ""
        self.chunk_size = 3  # Characters per chunk for smooth animation
    
    def simulate_streaming(self, full_response: str) -> Generator[str, None, None]:
        """Simulate streaming by yielding chunks of the response"""
        words = full_response.split()
        current_chunk = ""
        
        for word in words:
            current_chunk += word + " "
            
            # Yield chunk when it reaches desired size or at sentence boundaries
            if (len(current_chunk) >= self.chunk_size * 5 or 
                word.endswith('.') or word.endswith('!') or word.endswith('?')):
                yield current_chunk
                current_chunk = ""
                time.sleep(0.1)  # Small delay between chunks
        
        # Yield any remaining text
        if current_chunk.strip():
            yield current_chunk
    
    def process_code_blocks(self, text: str) -> Generator[str, None, None]:
        """Process text with special handling for code blocks"""
        import re
        
        # Split text into code blocks and regular text
        code_pattern = r'```(\w+)?\n(.*?)\n```'
        parts = re.split(code_pattern, text, flags=re.DOTALL)
        
        i = 0
        while i < len(parts):
            if i + 2 < len(parts) and parts[i + 1]:  # This is a code block
                # Yield text before code block
                if parts[i].strip():
                    yield from self.simulate_streaming(parts[i])
                
                # Yield code block header
                language = parts[i + 1] if parts[i + 1] else 'text'
                yield f"\n```{language}\n"
                time.sleep(0.2)
                
                # Yield code content line by line
                code_lines = parts[i + 2].split('\n')
                for line in code_lines:
                    yield line + '\n'
                    time.sleep(0.1)
                
                yield "```\n"
                time.sleep(0.2)
                
                i += 3
            else:
                # Regular text
                if parts[i].strip():
                    yield from self.simulate_streaming(parts[i])
                i += 1


class EnhancedResponseFormatter:
    """Enhanced response formatter with status indicator support"""

    def __init__(self, console: Console):
        self.console = console
        self.status_indicator = StatusIndicator(console)
        self.streaming_handler = StreamingResponseHandler()
    
    def display_response_with_typing(self, response_text: str, input_tokens: int,
                                   output_tokens: int, cost: float):
        """Display response with status indicators"""
        try:
            # Estimate tokens for status display start
            estimated_output = max(output_tokens, len(response_text) // 4)
            estimated_cost = cost

            # Start status indicator display
            self.status_indicator.start_status_display(input_tokens, estimated_output, estimated_cost)

            # Process the response through status stages
            self.status_indicator.process_response(response_text)

            # Finish with actual stats
            self.status_indicator.finish_status(output_tokens, cost)

            # Display the actual response content
            self.console.print(response_text)

        except KeyboardInterrupt:
            self.status_indicator.stop_status()
            self.console.print("\n[yellow]⚠️  Response interrupted by user[/yellow]")
        except Exception as e:
            self.status_indicator.stop_status()
            self.console.print(f"\n[red]❌ Status display error: {str(e)}[/red]")
            # Fallback to regular display
            self.console.print(response_text)
    
    def display_response_instant(self, response_text: str, input_tokens: int, 
                               output_tokens: int, cost: float):
        """Display response instantly (fallback method)"""
        # Create header with token info
        header = Text()
        header.append("Claude", style="bold success")
        
        # Enhanced token display
        token_info = Text()
        token_info.append(" [", style="muted")
        token_info.append("↑", style="warning")
        token_info.append(f"{input_tokens}", style="bold warning")
        token_info.append(" ↓", style="success")
        token_info.append(f"{output_tokens}", style="bold success")
        token_info.append(" tokens, ", style="muted")
        token_info.append(f"${cost:.6f}", style="bold primary")
        token_info.append("]", style="muted")
        
        self.console.print()
        self.console.print(Text.assemble(header, token_info))
        self.console.print(Rule(style="border"))
        
        # Display response
        self.console.print(response_text)
        
        self.console.print(Rule(style="border"))
        self.console.print()
