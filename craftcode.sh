#!/bin/bash
# CraftCode v1.6 - Unix/Linux/macOS Launcher
# Single command to start the AI terminal application

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}🚀 CraftCode v1.6${NC}"
    echo -e "   Powered by OpenRouter API with DeepSeek model"
    echo -e "   CraftCode Team"
    echo ""
}

print_error() {
    echo -e "${RED}❌ Error: $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if Python is available
check_python() {
    if ! command -v python3 &> /dev/null; then
        if ! command -v python &> /dev/null; then
            print_error "Python is not installed or not in PATH"
            echo "Please install Python 3.8 or higher"
            echo "Visit: https://python.org"
            exit 1
        else
            PYTHON_CMD="python"
        fi
    else
        PYTHON_CMD="python3"
    fi
    
    # Check Python version
    PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | cut -d' ' -f2)
    MAJOR_VERSION=$(echo $PYTHON_VERSION | cut -d'.' -f1)
    MINOR_VERSION=$(echo $PYTHON_VERSION | cut -d'.' -f2)
    
    if [ "$MAJOR_VERSION" -lt 3 ] || ([ "$MAJOR_VERSION" -eq 3 ] && [ "$MINOR_VERSION" -lt 8 ]); then
        print_error "Python 3.8 or higher is required"
        echo "Current version: $PYTHON_VERSION"
        exit 1
    fi
}

# Check if we're in the right directory
check_files() {
    if [ ! -f "codecraft/main.py" ] && [ ! -f "craftcode.py" ]; then
        print_error "CraftCode application files not found"
        echo "Please ensure you're in the correct directory"
        exit 1
    fi
}

# Make script executable if needed
make_executable() {
    if [ ! -x "$0" ]; then
        chmod +x "$0"
    fi
}

# Main function
main() {
    print_status
    
    # Make script executable
    make_executable
    
    # Check requirements
    check_python
    check_files
    
    # Launch the application
    echo "Starting CraftCode..."
    echo ""
    
    if [ -f "craftcode.py" ]; then
        $PYTHON_CMD craftcode.py "$@"
    else
        $PYTHON_CMD -m codecraft.main "$@"
    fi
    
    exit_code=$?
    
    if [ $exit_code -eq 0 ]; then
        echo ""
        echo "👋 Thank you for using CraftCode v1.6!"
    else
        print_error "Application encountered an error (exit code: $exit_code)"
    fi
    
    exit $exit_code
}

# Handle command line arguments
case "$1" in
    --help|-h)
        echo "CraftCode v1.6 - AI Terminal Application"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --help, -h      Show this help message"
        echo "  --version, -v   Show version information"
        echo "  --check         Check system requirements"
        echo ""
        echo "Examples:"
        echo "  $0              # Start CraftCode"
        echo "  $0 --check      # Check requirements"
        echo ""
        exit 0
        ;;
    --version|-v)
        echo "CraftCode v1.6.0"
        echo "Powered by OpenRouter API with DeepSeek model"
        echo "CraftCode Team"
        echo "Python: $($PYTHON_CMD --version 2>&1)"
        echo "Platform: $(uname -s) $(uname -r)"
        exit 0
        ;;
    --check)
        print_status
        echo "🔍 Checking system requirements..."
        check_python
        check_files
        print_success "Python version: OK"
        print_success "Application files: OK"
        echo ""
        print_success "System is ready to run CraftCode!"
        exit 0
        ;;
esac

# Run main function
main "$@"
