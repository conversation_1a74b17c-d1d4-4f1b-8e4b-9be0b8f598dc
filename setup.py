from setuptools import setup, find_packages

setup(
    name="craftcode",
    version="1.6.0",
    description="AI-powered terminal application with Claude-like interface",
    author="CraftCode Team",
    packages=find_packages(),
    install_requires=[
        "requests>=2.28.0",
        "colorama>=0.4.4",
        "rich>=13.0.0",
        "pygments>=2.14.0",
        "tiktoken>=0.4.0",
        "prompt-toolkit>=3.0.36",
        "click>=8.1.0",
    ],
    entry_points={
        "console_scripts": [
            "craftcode=codecraft.main:main",
        ],
    },
    python_requires=">=3.8",
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: Microsoft :: Windows",
    ],
)
