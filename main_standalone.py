#!/usr/bin/env python3
"""
CraftCode v1.6 - Standalone entry point for executable
"""

import sys
import os
import signal
from typing import Optional
from prompt_toolkit import prompt
from prompt_toolkit.shortcuts import confirm
from prompt_toolkit.formatted_text import HTML
from prompt_toolkit.styles import Style

# Add the codecraft directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'codecraft'))

from codecraft.ui import ClaudeUI
from codecraft.api_client import OpenRouterClient
from codecraft.token_manager import TokenManager
from codecraft.conversation import ConversationManager


class CraftCodeTerminal:
    """Main terminal application class"""
    
    def __init__(self):
        self.ui = ClaudeUI()
        self.api_client = OpenRouterClient()
        self.token_manager = TokenManager()
        self.conversation_manager = ConversationManager()
        self.running = True
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.ui.print_warning("Shutting down gracefully...")
        self.shutdown()
    
    def start(self):
        """Start the terminal application"""
        try:
            self.ui.clear_screen()
            self.ui.print_welcome_message()
            
            while self.running:
                try:
                    # Get user input
                    user_input = self._get_user_input()
                    
                    if not user_input.strip():
                        continue
                    
                    # Handle special case: just "/" shows the menu (placeholder for now)
                    if user_input.strip() == "/":
                        self.ui.print_info("Slash command menu not available in standalone version. Use '/help' for commands.")
                        continue

                    # Handle commands
                    if self._handle_command(user_input.strip()):
                        continue
                    
                    # Send to AI
                    self._handle_ai_interaction(user_input)
                    
                except KeyboardInterrupt:
                    self.ui.print_warning("Use 'exit' or 'quit' to close the application.")
                    continue
                except EOFError:
                    break
                except Exception as e:
                    self.ui.print_error(f"Unexpected error: {str(e)}")
                    continue
        
        finally:
            self.shutdown()
    
    def _get_user_input(self) -> str:
        """Get user input with custom prompt"""
        # Estimate tokens for current conversation
        conversation_history = self.conversation_manager.get_api_format_history()
        
        # Create custom prompt style
        style = Style.from_dict({
            'prompt': '#FF6B35 bold',
            'tokens': '#9B9B9B',
        })
        
        # Show session stats in prompt
        session_stats = self.token_manager.get_session_summary()
        if session_stats['message_count'] > 0:
            prompt_text = HTML(
                f'<prompt>You</prompt> '
                f'<tokens>[{self.token_manager.format_tokens(session_stats["total_tokens"])} tokens, '
                f'{self.token_manager.format_cost(session_stats["total_cost"])}]</tokens> '
                f'<prompt>></prompt> '
            )
        else:
            prompt_text = HTML('<prompt>You</prompt> <prompt>></prompt> ')
        
        return prompt(prompt_text, style=style)
    
    def _handle_command(self, user_input: str) -> bool:
        """Handle built-in commands"""
        command = user_input.lower()
        
        if command in ['exit', 'quit']:
            self.running = False
            return True
        
        elif command == 'help':
            self.ui.print_help()
            return True
        
        elif command == 'clear':
            self.ui.clear_screen()
            return True
        
        elif command == 'history':
            self._show_conversation_history()
            return True
        
        elif command == 'stats':
            self._show_statistics()
            return True
        
        elif command == 'save':
            self._save_conversation()
            return True
        
        elif command == 'load':
            self._load_conversation()
            return True
        
        elif command == 'reset':
            self._reset_conversation()
            return True
        
        elif command.startswith('export '):
            format_type = command.split(' ', 1)[1] if ' ' in command else 'txt'
            self._export_conversation(format_type)
            return True
        
        return False
    
    def _handle_ai_interaction(self, user_input: str):
        """Handle AI interaction"""
        try:
            # Show thinking animation
            progress = self.ui.show_thinking()
            
            try:
                # Send message to AI
                response = self.api_client.send_message(user_input)
                
                # Stop thinking animation
                progress.stop()
                
                # Add to conversation history
                self.conversation_manager.add_message("user", user_input, response.input_tokens, 0)
                self.conversation_manager.add_message("assistant", response.content, 0, response.cost)
                
                # Update token manager
                self.token_manager.add_interaction(
                    response.input_tokens,
                    response.output_tokens,
                    response.cost,
                    user_input
                )
                
                # Display response
                self.ui.print_ai_response(
                    response.content,
                    response.input_tokens,
                    response.output_tokens,
                    response.cost
                )
                
            except Exception as e:
                progress.stop()
                raise e
                
        except Exception as e:
            self.ui.print_error(str(e), self._get_error_suggestion(str(e)))
    
    def _get_error_suggestion(self, error_message: str) -> Optional[str]:
        """Get suggestion for error message"""
        error_lower = error_message.lower()
        
        if "network" in error_lower or "connection" in error_lower:
            return "Check your internet connection and try again"
        elif "rate limit" in error_lower:
            return "Wait a moment before sending another message"
        elif "authentication" in error_lower:
            return "The API key may be invalid or expired"
        elif "timeout" in error_lower:
            return "Try again with a shorter message"
        
        return None
    
    def _show_conversation_history(self):
        """Show conversation history"""
        history = self.conversation_manager.get_conversation_history()
        
        if not history:
            self.ui.print_info("No conversation history yet.")
            return
        
        self.ui.console.print("\n[bold]📝 Conversation History[/bold]")
        self.ui.console.print("─" * 80, style="border")
        
        for i, msg in enumerate(history, 1):
            timestamp = msg.timestamp.split('T')[1][:8] if 'T' in msg.timestamp else msg.timestamp
            role_style = "primary" if msg.role == "user" else "success"
            role_name = "You" if msg.role == "user" else "Claude"
            
            # Show message preview
            preview = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
            
            self.ui.console.print(f"[{role_style}]{i}. {role_name}[/{role_style}] [{timestamp}]: {preview}")
        
        self.ui.console.print("─" * 80, style="border")
        self.ui.console.print()
    
    def _show_statistics(self):
        """Show token usage and cost statistics"""
        session_stats = self.token_manager.get_session_summary()
        total_usage = self.token_manager.load_total_usage()
        
        # Create statistics table
        from rich.table import Table
        
        table = Table(title="📊 Usage Statistics", border_style="border")
        table.add_column("Metric", style="primary")
        table.add_column("Current Session", style="text")
        table.add_column("All Time", style="muted")
        
        table.add_row(
            "Messages",
            str(session_stats["message_count"]),
            f"{total_usage['total_sessions']} sessions"
        )
        table.add_row(
            "Input Tokens",
            self.token_manager.format_tokens(session_stats["total_input_tokens"]),
            "-"
        )
        table.add_row(
            "Output Tokens",
            self.token_manager.format_tokens(session_stats["total_output_tokens"]),
            "-"
        )
        table.add_row(
            "Total Tokens",
            self.token_manager.format_tokens(session_stats["total_tokens"]),
            self.token_manager.format_tokens(total_usage["total_tokens"])
        )
        table.add_row(
            "Total Cost",
            self.token_manager.format_cost(session_stats["total_cost"]),
            self.token_manager.format_cost(total_usage["total_cost"])
        )
        
        if session_stats["message_count"] > 0:
            table.add_row(
                "Avg Cost/Message",
                self.token_manager.format_cost(session_stats["average_cost_per_message"]),
                "-"
            )
        
        self.ui.console.print(table)
        self.ui.console.print()
    
    def _save_conversation(self):
        """Save current conversation"""
        try:
            if not self.conversation_manager.current_conversation:
                self.ui.print_warning("No conversation to save.")
                return
            
            filepath = self.conversation_manager.save_conversation()
            self.ui.print_success(f"Conversation saved to: {os.path.basename(filepath)}")
            
        except Exception as e:
            self.ui.print_error(f"Failed to save conversation: {str(e)}")
    
    def _load_conversation(self):
        """Load a saved conversation"""
        try:
            conversations = self.conversation_manager.list_saved_conversations()
            
            if not conversations:
                self.ui.print_info("No saved conversations found.")
                return
            
            # Show available conversations
            self.ui.console.print("\n[bold]📁 Saved Conversations[/bold]")
            for i, conv in enumerate(conversations[:10], 1):  # Show last 10
                self.ui.console.print(
                    f"{i}. {conv['title']} "
                    f"[muted]({conv['message_count']} messages, {conv['created'][:10]})[/muted]"
                )
            
            self.ui.console.print("\n[muted]Enter filename to load (or press Enter to cancel):[/muted]")
            
        except Exception as e:
            self.ui.print_error(f"Failed to list conversations: {str(e)}")
    
    def _reset_conversation(self):
        """Reset current conversation"""
        if self.conversation_manager.current_conversation:
            if confirm("Are you sure you want to clear the conversation history?"):
                self.conversation_manager.clear_conversation()
                self.api_client.clear_history()
                self.token_manager.reset_session()
                self.ui.print_success("Conversation history cleared.")
        else:
            self.ui.print_info("No conversation to reset.")
    
    def _export_conversation(self, format_type: str):
        """Export current conversation"""
        try:
            if not self.conversation_manager.current_conversation:
                self.ui.print_warning("No conversation to export.")
                return
            
            filepath = self.conversation_manager.export_conversation(format_type)
            self.ui.print_success(f"Conversation exported to: {os.path.basename(filepath)}")
            
        except Exception as e:
            self.ui.print_error(f"Failed to export conversation: {str(e)}")
    
    def shutdown(self):
        """Shutdown the application"""
        try:
            # Save session history
            self.token_manager.save_session_history()
            
            # Show goodbye message
            session_stats = self.token_manager.get_session_summary()
            if session_stats['message_count'] > 0:
                self.ui.console.print(
                    f"\n[muted]Session summary: {session_stats['message_count']} messages, "
                    f"{self.token_manager.format_tokens(session_stats['total_tokens'])} tokens, "
                    f"{self.token_manager.format_cost(session_stats['total_cost'])}[/muted]"
                )
            
            self.ui.console.print("\n[primary]👋 Thank you for using CodeCraft AI Terminal![/primary]")
            
        except Exception as e:
            print(f"Error during shutdown: {e}")
        
        finally:
            sys.exit(0)


def main():
    """Main entry point"""
    try:
        terminal = CraftCodeTerminal()
        terminal.start()
    except Exception as e:
        print(f"Failed to start CodeCraft AI Terminal: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
