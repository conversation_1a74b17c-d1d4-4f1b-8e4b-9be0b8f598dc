"""
OpenRouter API client for DeepSeek model integration
"""

import requests
import json
import time
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass


@dataclass
class APIResponse:
    """Response from the API"""
    content: str
    input_tokens: int
    output_tokens: int
    total_tokens: int
    cost: float


class OpenRouterClient:
    """OpenRouter API client for DeepSeek model"""
    
    def __init__(self):
        self.api_key = "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"
        self.base_url = "https://openrouter.ai/api/v1"
        self.model = "deepseek/deepseek-r1:free"
        self.conversation_history: List[Dict[str, str]] = []
        
        # DeepSeek pricing (per 1M tokens) - these are example rates
        self.input_cost_per_million = 0.14  # $0.14 per 1M input tokens
        self.output_cost_per_million = 0.28  # $0.28 per 1M output tokens
        
        # Session for connection pooling
        self.session = requests.Session()
        self.session.headers.update({
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'HTTP-Referer': 'https://github.com/codecraft-cli',
            'X-Title': 'CodeCraft AI Terminal'
        })
    
    def send_message(self, message: str, max_retries: int = 3) -> APIResponse:
        """Send message to AI and return response with token info"""
        # Add user message to conversation history
        self.conversation_history.append({
            "role": "user",
            "content": message
        })
        
        # Prepare request data
        request_data = {
            "model": self.model,
            "messages": self.conversation_history,
            "temperature": 0.7,
            "max_tokens": 2000,
            "top_p": 1,
            "frequency_penalty": 0,
            "presence_penalty": 0
        }
        
        # Attempt request with retries
        last_error = None
        for attempt in range(max_retries):
            try:
                response = self.session.post(
                    f"{self.base_url}/chat/completions",
                    json=request_data,
                    timeout=30
                )
                
                if response.status_code == 200:
                    return self._process_response(response.json())
                else:
                    error_msg = self._handle_http_error(response)
                    if attempt < max_retries - 1 and self._is_retryable_error(response.status_code):
                        time.sleep(2 ** attempt)  # Exponential backoff
                        continue
                    else:
                        raise Exception(error_msg)
                        
            except requests.exceptions.RequestException as e:
                last_error = e
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)
                    continue
        
        # If we get here, all retries failed
        raise Exception(f"Network error after {max_retries} attempts: {str(last_error)}")
    
    def _process_response(self, response_data: Dict[str, Any]) -> APIResponse:
        """Process API response and extract token information"""
        if not response_data.get('choices') or len(response_data['choices']) == 0:
            raise Exception("Invalid response format from API")
        
        # Extract message content
        ai_message = response_data['choices'][0]['message']['content']
        
        # Add AI response to conversation history
        self.conversation_history.append({
            "role": "assistant",
            "content": ai_message
        })
        
        # Extract token usage information
        usage = response_data.get('usage', {})
        input_tokens = usage.get('prompt_tokens', 0)
        output_tokens = usage.get('completion_tokens', 0)
        total_tokens = usage.get('total_tokens', input_tokens + output_tokens)
        
        # Calculate cost
        input_cost = (input_tokens / 1_000_000) * self.input_cost_per_million
        output_cost = (output_tokens / 1_000_000) * self.output_cost_per_million
        total_cost = input_cost + output_cost
        
        # Keep conversation history manageable (last 20 messages)
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
        
        return APIResponse(
            content=ai_message,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=total_tokens,
            cost=total_cost
        )
    
    def _handle_http_error(self, response: requests.Response) -> str:
        """Handle HTTP error responses"""
        status_code = response.status_code
        
        try:
            error_data = response.json()
            error_message = error_data.get('error', {}).get('message', 'Unknown error')
        except:
            error_message = response.text or 'Unknown error'
        
        error_messages = {
            400: f"Bad request: {error_message}",
            401: "Authentication failed. Please check your API key.",
            403: "Access forbidden. Your API key may not have permission for this model.",
            429: "Rate limit exceeded. Please wait a moment before trying again.",
            500: "Server error. Please try again later.",
            502: "Bad gateway. The service is temporarily unavailable.",
            503: "Service temporarily unavailable. Please try again later.",
            504: "Gateway timeout. The request took too long to process."
        }
        
        return error_messages.get(status_code, f"HTTP {status_code}: {error_message}")
    
    def _is_retryable_error(self, status_code: int) -> bool:
        """Check if error is retryable"""
        retryable_codes = {429, 500, 502, 503, 504}
        return status_code in retryable_codes
    
    def clear_history(self):
        """Clear conversation history"""
        self.conversation_history = []
    
    def get_history(self) -> List[Dict[str, str]]:
        """Get conversation history"""
        return self.conversation_history.copy()
    
    def set_system_prompt(self, prompt: str):
        """Set system prompt for the conversation"""
        # Remove any existing system message
        self.conversation_history = [
            msg for msg in self.conversation_history 
            if msg.get('role') != 'system'
        ]
        
        # Add new system message at the beginning
        self.conversation_history.insert(0, {
            "role": "system",
            "content": prompt
        })
    
    def estimate_tokens(self, text: str) -> int:
        """Estimate token count for text (rough approximation)"""
        # Rough estimation: ~4 characters per token for English text
        return len(text) // 4
    
    def calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Calculate cost for given token counts"""
        input_cost = (input_tokens / 1_000_000) * self.input_cost_per_million
        output_cost = (output_tokens / 1_000_000) * self.output_cost_per_million
        return input_cost + output_cost
