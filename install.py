#!/usr/bin/env python3
"""
CraftCode v1.6 - Installation Script
Automated installation and setup for all platforms
"""

import sys
import os
import subprocess
import platform
import urllib.request
import zipfile
import shutil
from pathlib import Path


def print_banner():
    """Print installation banner"""
    print("=" * 60)
    print("🚀 CraftCode v1.6 - Installation Script")
    print("   Powered by OpenRouter API with DeepSeek model")
    print("   CraftCode Team")
    print("=" * 60)
    print()


def check_python():
    """Check Python version"""
    print("🔍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print(f"❌ Python 3.8+ required. Current: {sys.version}")
        print("Please upgrade Python from https://python.org")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} - OK")
    return True


def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing dependencies...")
    
    dependencies = [
        'requests>=2.28.0',
        'colorama>=0.4.4',
        'rich>=13.0.0',
        'pygments>=2.14.0',
        'tiktoken>=0.4.0',
        'prompt-toolkit>=3.0.36',
        'click>=8.1.0'
    ]
    
    try:
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', '--upgrade', '--user'
        ] + dependencies)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def create_launcher_scripts():
    """Create platform-specific launcher scripts"""
    print("\n🔧 Creating launcher scripts...")
    
    # Make shell script executable on Unix systems
    if platform.system() != 'Windows':
        try:
            os.chmod('craftcode.sh', 0o755)
            print("✅ Made craftcode.sh executable")
        except FileNotFoundError:
            print("⚠️  craftcode.sh not found")
    
    return True


def setup_desktop_shortcut():
    """Create desktop shortcut (optional)"""
    response = input("\n🖥️  Create desktop shortcut? (y/N): ").lower().strip()
    
    if response != 'y':
        return True
    
    try:
        desktop = Path.home() / 'Desktop'
        if not desktop.exists():
            print("⚠️  Desktop folder not found")
            return True
        
        current_dir = Path.cwd()
        
        if platform.system() == 'Windows':
            # Create Windows shortcut
            shortcut_path = desktop / 'CraftCode v1.6.bat'
            with open(shortcut_path, 'w') as f:
                f.write(f'@echo off\ncd /d "{current_dir}"\ncraftcode.bat\npause')
            print(f"✅ Desktop shortcut created: {shortcut_path}")
            
        else:
            # Create Unix desktop entry
            shortcut_path = desktop / 'CraftCode v1.6.desktop'
            with open(shortcut_path, 'w') as f:
                f.write(f"""[Desktop Entry]
Version=1.0
Type=Application
Name=CraftCode v1.6
Comment=AI Terminal Application
Exec={current_dir}/craftcode.sh
Icon=terminal
Terminal=true
Categories=Development;
""")
            os.chmod(shortcut_path, 0o755)
            print(f"✅ Desktop shortcut created: {shortcut_path}")
        
        return True
        
    except Exception as e:
        print(f"⚠️  Could not create desktop shortcut: {e}")
        return True


def verify_installation():
    """Verify the installation"""
    print("\n🔍 Verifying installation...")
    
    # Check if main files exist
    required_files = [
        'codecraft/main.py',
        'codecraft/__init__.py',
        'requirements.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    # Test import
    try:
        sys.path.insert(0, str(Path.cwd()))
        from codecraft.main import main
        print("✅ Application modules can be imported")
    except ImportError as e:
        print(f"❌ Import test failed: {e}")
        return False
    
    print("✅ Installation verified successfully!")
    return True


def show_completion_message():
    """Show installation completion message"""
    print("\n" + "=" * 60)
    print("🎉 CraftCode v1.6 Installation Complete!")
    print("=" * 60)
    print()
    print("📋 Quick Start:")
    
    if platform.system() == 'Windows':
        print("   • Double-click: craftcode.bat")
        print("   • Command line: python craftcode.py")
    else:
        print("   • Terminal: ./craftcode.sh")
        print("   • Command line: python3 craftcode.py")
    
    print()
    print("📖 Available Commands:")
    print("   • /help - Show all commands")
    print("   • /stats - View usage statistics")
    print("   • /history - Show conversation history")
    print("   • /export - Export conversations")
    print()
    print("🌐 Features:")
    print("   ✓ Real-time token counting and cost tracking")
    print("   ✓ Code syntax highlighting")
    print("   ✓ Slash command system")
    print("   ✓ Advanced conversation management")
    print("   ✓ Claude-inspired interface")
    print()
    print("🚀 Ready to start your AI-powered terminal experience!")
    print("=" * 60)


def main():
    """Main installation function"""
    print_banner()
    
    # Check Python version
    if not check_python():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Installation failed at dependency installation")
        sys.exit(1)
    
    # Create launcher scripts
    create_launcher_scripts()
    
    # Setup desktop shortcut (optional)
    setup_desktop_shortcut()
    
    # Verify installation
    if not verify_installation():
        print("\n❌ Installation verification failed")
        sys.exit(1)
    
    # Show completion message
    show_completion_message()


if __name__ == "__main__":
    main()
