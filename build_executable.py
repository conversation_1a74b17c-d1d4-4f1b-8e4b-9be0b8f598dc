#!/usr/bin/env python3
"""
Build script for creating standalone executable
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_requirements():
    """Check if all required packages are installed"""
    required_packages = [
        'pyinstaller',
        'requests',
        'colorama',
        'rich',
        'pygments',
        'tiktoken',
        'prompt-toolkit',
        'click'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("Missing required packages:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\nInstall them with: pip install -r requirements.txt")
        return False
    
    return True


def clean_build_directories():
    """Clean previous build directories"""
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            print(f"Cleaning {dir_name}...")
            shutil.rmtree(dir_name)
    
    # Clean .pyc files
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                os.remove(os.path.join(root, file))


def build_executable():
    """Build the standalone executable"""
    print("Building CodeCraft AI Terminal executable...")
    
    # PyInstaller command
    cmd = [
        'pyinstaller',
        '--onefile',                    # Create a single executable file
        '--console',                    # Console application
        '--name=CodeCraft-AI-Terminal', # Executable name
        '--distpath=dist',              # Output directory
        '--workpath=build',             # Build directory
        '--specpath=.',                 # Spec file location
        '--clean',                      # Clean cache
        '--noconfirm',                  # Overwrite without confirmation
        
        # Hidden imports (packages that PyInstaller might miss)
        '--hidden-import=tiktoken_ext.openai_public',
        '--hidden-import=tiktoken_ext',
        '--hidden-import=colorama',
        '--hidden-import=rich',
        '--hidden-import=pygments',
        '--hidden-import=prompt_toolkit',
        '--hidden-import=requests',
        '--hidden-import=click',
        '--hidden-import=json',
        '--hidden-import=datetime',
        '--hidden-import=dataclasses',
        '--hidden-import=typing',
        
        # Exclude unnecessary modules to reduce size
        '--exclude-module=tkinter',
        '--exclude-module=matplotlib',
        '--exclude-module=numpy',
        '--exclude-module=pandas',
        '--exclude-module=scipy',
        '--exclude-module=PIL',
        '--exclude-module=cv2',
        
        # Entry point
        'codecraft/main.py'
    ]
    
    try:
        # Run PyInstaller
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Build completed successfully!")
        
        # Check if executable was created
        exe_path = Path('dist/CodeCraft-AI-Terminal.exe')
        if exe_path.exists():
            size_mb = exe_path.stat().st_size / (1024 * 1024)
            print(f"Executable created: {exe_path}")
            print(f"Size: {size_mb:.1f} MB")
            return True
        else:
            print("Error: Executable not found after build")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"Build failed with error: {e}")
        print("STDOUT:", e.stdout)
        print("STDERR:", e.stderr)
        return False


def create_distribution_package():
    """Create a distribution package with documentation"""
    print("Creating distribution package...")
    
    # Create distribution directory
    dist_dir = Path('distribution')
    if dist_dir.exists():
        shutil.rmtree(dist_dir)
    dist_dir.mkdir()
    
    # Copy executable
    exe_source = Path('dist/CodeCraft-AI-Terminal.exe')
    exe_dest = dist_dir / 'CodeCraft-AI-Terminal.exe'
    
    if exe_source.exists():
        shutil.copy2(exe_source, exe_dest)
        print(f"Copied executable to {exe_dest}")
    else:
        print("Error: Executable not found")
        return False
    
    # Create README for distribution
    readme_content = """# CodeCraft AI Terminal

## Quick Start

1. Double-click `CodeCraft-AI-Terminal.exe` to start the application
2. Type your message and press Enter to chat with AI
3. Use 'help' to see available commands
4. Press Ctrl+C or type 'exit' to quit

## Features

- **AI-Powered Conversations**: Chat with DeepSeek AI model through OpenRouter API
- **Token Tracking**: Real-time token counting and cost calculation
- **Code Highlighting**: Syntax highlighting for code blocks in responses
- **Conversation Management**: Save, load, and export conversation history
- **Claude-Inspired Interface**: Modern, clean terminal interface

## Commands

- `help` - Show available commands
- `clear` - Clear the terminal screen
- `history` - Show conversation history
- `stats` - Show token usage and cost statistics
- `save` - Save current conversation to file
- `load` - Load a saved conversation
- `reset` - Clear conversation history
- `export txt/md` - Export conversation to text or markdown
- `exit/quit` - Exit the application

## System Requirements

- Windows 10 or newer
- No additional software installation required

## Support

This is a standalone application that includes all necessary dependencies.
If you encounter any issues, please check that:

1. You have an active internet connection
2. Windows Defender or antivirus is not blocking the application
3. You have sufficient disk space for conversation history

## Privacy

- All conversations are stored locally on your computer
- No data is sent to third parties except the OpenRouter API for AI responses
- You can delete conversation history at any time

Enjoy using CodeCraft AI Terminal!
"""
    
    readme_path = dist_dir / 'README.txt'
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print(f"Created README at {readme_path}")
    
    # Create batch file for easy launching
    batch_content = """@echo off
title CodeCraft AI Terminal
CodeCraft-AI-Terminal.exe
pause
"""
    
    batch_path = dist_dir / 'Start-CodeCraft.bat'
    with open(batch_path, 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print(f"Created launcher batch file at {batch_path}")
    
    print(f"\nDistribution package created in: {dist_dir.absolute()}")
    return True


def main():
    """Main build process"""
    print("CodeCraft AI Terminal - Build Script")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Clean previous builds
    clean_build_directories()
    
    # Build executable
    if not build_executable():
        sys.exit(1)
    
    # Create distribution package
    if not create_distribution_package():
        sys.exit(1)
    
    print("\n" + "=" * 40)
    print("Build completed successfully!")
    print("The standalone executable is ready for distribution.")
    print("Check the 'distribution' folder for the complete package.")


if __name__ == "__main__":
    main()
