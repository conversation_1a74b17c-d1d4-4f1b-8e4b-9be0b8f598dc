# CraftCode v1.6

A sophisticated AI-powered terminal application with a Claude-inspired interface, featuring real-time token tracking, cost calculation, slash commands, and advanced conversation management. Built for cross-platform compatibility (Windows, macOS, Linux).

## 🚀 Features

### Core Functionality
- **AI-Powered Conversations**: Chat with DeepSeek AI model through OpenRouter API
- **Claude-Inspired Interface**: Modern, clean terminal UI with syntax highlighting
- **Cross-Platform Support**: Works on Windows, macOS, and Linux
- **Real-time Token Tracking**: Monitor input/output tokens and costs in real-time
- **Slash Commands System**: Discord/Slack-style commands with autocomplete
- **Advanced Conversation Management**: Save, load, export, search, and analyze conversations

### Technical Features
- **Code Syntax Highlighting**: Automatic highlighting for 100+ programming languages
- **Cost Calculation**: Precise cost tracking with detailed breakdowns
- **Session Analytics**: Comprehensive usage statistics and conversation analysis
- **Error Handling**: Robust error handling with retry mechanisms
- **Multiple Export Formats**: Export to TXT, MD, JSON, CSV formats
- **Single Command Launch**: One command to start the application on any platform

## 📋 System Requirements

- **Operating System**: Windows 10+, macOS 10.14+, or Linux (Ubuntu 18.04+)
- **Python**: 3.8 or higher
- **Memory**: 100MB RAM minimum
- **Storage**: 50MB free disk space
- **Network**: Internet connection for AI API access

## 🔧 Installation & Usage

### Quick Start (Recommended)

**One-Command Installation:**
```bash
# Download and run the installer
curl -sSL https://raw.githubusercontent.com/im-rahulr/craftcode/main/install.py | python3
```

**Manual Installation:**
```bash
# Clone the repository
git clone https://github.com/im-rahulr/craftcode.git
cd craftcode

# Run the installer
python install.py
```

### Launch CraftCode

**Windows:**
```cmd
craftcode.bat
```

**macOS/Linux:**
```bash
./craftcode.sh
```

**Cross-Platform:**
```bash
python craftcode.py
```

## 🎯 Available Commands

### Slash Commands
| Command | Description |
|---------|-------------|
| `/help` | Show available commands and usage information |
| `/clear` | Clear the terminal screen |
| `/models` | Show available AI models and switch between them |
| `/switch <model>` | Switch to a different AI model (gemini, gpt4o, claude4, deepseek) |
| `/pricing` | Show model pricing comparison |
| `/history` | Display conversation history with timestamps |
| `/stats` | Show detailed token usage and cost statistics |
| `/save` | Save current conversation to file |
| `/load` | List and load saved conversations |
| `/search <query>` | Search conversation history |
| `/analyze` | Analyze conversation patterns and usage |
| `/export <format>` | Export conversation (txt, md, json, csv) |
| `/reset` | Clear current conversation history |
| `/exit` or `/quit` | Exit the application |

### AI Models
- **Gemini 2.5 Flash** - Google's fastest model with multimodal capabilities
- **GPT-4o** - OpenAI's most advanced multimodal model
- **Claude 4 (Sonnet)** - Anthropic's most capable model with enhanced safety
- **DeepSeek R1** - High-performance open-source model (free tier)

## 💰 Token & Cost Tracking

The application provides comprehensive token and cost tracking:

- **Real-time Display**: See token counts and costs for each message
- **Model-specific Pricing**: Different rates for each AI model
- **Session Statistics**: Track cumulative usage during your session
- **Historical Data**: Maintain usage history across sessions
- **Cost Breakdown**: Separate tracking for input and output tokens
- **Export Reports**: Save usage statistics for analysis

## 🎨 Interface Features

### Real-time Typing Animation
- **Live Streaming**: Watch AI responses appear character by character
- **Enhanced Experience**: Similar to ChatGPT's typing interface
- **Token Display**: Real-time token counting during response generation

### Claude-Inspired Design
- **Color Scheme**: Carefully chosen colors matching Claude's aesthetic
- **Typography**: Clean, readable fonts optimized for terminal use
- **Code Highlighting**: Automatic syntax highlighting for 100+ languages
- **Responsive Layout**: Adapts to different terminal sizes

## 🚀 Quick Examples

### Basic Chat
```
You > Hello! Can you help me with Python?
Claude [↑12 ↓45 tokens, $0.000018]
────────────────────────────────────────
Of course! I'd be happy to help you with Python...
────────────────────────────────────────
```

### Model Switching
```
You > /models
🤖 Available AI Models
┌────────────────────┬──────────┬─────────┬──────────┬────────────┬────────────┬─────────┐
│ Model              │ Tier     │ Speed   │ Quality  │ Input Cost │ Output Cost│ Context │
├────────────────────┼──────────┼─────────┼──────────┼────────────┼────────────┼─────────┤
│ Gemini 2.5 Flash   │ Premium  │ Fast    │ Excellent│ $0.075/1M  │ $0.30/1M   │ 1000K   │
│ GPT-4o             │ Enterprise│ Balanced│ Superior │ $2.50/1M   │ $10.00/1M  │ 128K    │
│ Claude 4 (Sonnet) ⭐│ Enterprise│ Balanced│ Superior │ $3.00/1M   │ $15.00/1M  │ 200K    │
│ DeepSeek R1        │ Free     │ Fast    │ Good     │ $0.14/1M   │ $0.28/1M   │ 32K     │
└────────────────────┴──────────┴─────────┴──────────┴────────────┴────────────┴─────────┘

You > /switch gemini
✅ Switched to Gemini 2.5 Flash
```

### Advanced Analytics
```
You > /analyze tokens
📊 Token Usage Analysis
┌─────────────────────┬─────────┐
│ Metric              │ Value   │
├─────────────────────┼─────────┤
│ Total Tokens        │ 1.2K    │
│ Total Cost          │ $0.0034 │
│ Average Tokens/Msg  │ 156.3   │
│ Highest Usage       │ 342     │
└─────────────────────┴─────────┘
```
