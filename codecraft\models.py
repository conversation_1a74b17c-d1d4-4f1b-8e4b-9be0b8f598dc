"""
Model management and fake model switching interface for CraftCode v1.6
"""

from dataclasses import dataclass
from typing import Dict, List, Optional
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.columns import Columns


@dataclass
class ModelInfo:
    """Information about an AI model"""
    id: str
    name: str
    display_name: str
    description: str
    input_cost_per_million: float
    output_cost_per_million: float
    context_window: int
    max_output: int
    features: List[str]
    tier: str  # "free", "premium", "enterprise"
    speed: str  # "fast", "balanced", "slow"
    quality: str  # "good", "excellent", "superior"


class ModelManager:
    """Manages fake model switching with premium UI experience"""
    
    def __init__(self):
        self.current_model = "deepseek"
        self.models = self._initialize_models()
        
    def _initialize_models(self) -> Dict[str, ModelInfo]:
        """Initialize the fake model catalog"""
        return {
            "gemini": ModelInfo(
                id="gemini",
                name="gemini-2.5-flash",
                display_name="Gemini 2.5 Flash",
                description="Google's fastest and most efficient model with multimodal capabilities",
                input_cost_per_million=0.075,
                output_cost_per_million=0.30,
                context_window=1000000,
                max_output=8192,
                features=[
                    "⚡ Ultra-fast responses",
                    "🖼️ Image understanding",
                    "📊 Data analysis",
                    "🔍 Web search integration",
                    "📝 Long context processing"
                ],
                tier="premium",
                speed="fast",
                quality="excellent"
            ),
            "gpt4o": ModelInfo(
                id="gpt4o",
                name="gpt-4o",
                display_name="GPT-4o",
                description="OpenAI's most advanced multimodal model with superior reasoning",
                input_cost_per_million=2.50,
                output_cost_per_million=10.00,
                context_window=128000,
                max_output=4096,
                features=[
                    "🧠 Advanced reasoning",
                    "🖼️ Vision capabilities",
                    "🎨 Creative writing",
                    "💻 Code generation",
                    "🔬 Scientific analysis"
                ],
                tier="enterprise",
                speed="balanced",
                quality="superior"
            ),
            "claude4": ModelInfo(
                id="claude4",
                name="claude-4-sonnet",
                display_name="Claude 4 (Sonnet)",
                description="Anthropic's most capable model with enhanced safety and reasoning",
                input_cost_per_million=3.00,
                output_cost_per_million=15.00,
                context_window=200000,
                max_output=4096,
                features=[
                    "🛡️ Enhanced safety",
                    "📚 Long document analysis",
                    "🎯 Precise reasoning",
                    "✍️ Creative writing",
                    "🔍 Research assistance"
                ],
                tier="enterprise",
                speed="balanced",
                quality="superior"
            ),
            "deepseek": ModelInfo(
                id="deepseek",
                name="deepseek-r1",
                display_name="DeepSeek R1",
                description="High-performance open-source model with excellent reasoning capabilities",
                input_cost_per_million=0.14,
                output_cost_per_million=0.28,
                context_window=32000,
                max_output=2000,
                features=[
                    "🆓 Free tier available",
                    "💻 Code specialization",
                    "🧮 Mathematical reasoning",
                    "📖 Technical documentation",
                    "⚡ Fast responses"
                ],
                tier="free",
                speed="fast",
                quality="good"
            )
        }
    
    def get_current_model(self) -> ModelInfo:
        """Get current model information"""
        return self.models[self.current_model]
    
    def get_all_models(self) -> Dict[str, ModelInfo]:
        """Get all available models"""
        return self.models
    
    def switch_model(self, model_id: str) -> bool:
        """Switch to a different model (fake - all use DeepSeek backend)"""
        if model_id in self.models:
            self.current_model = model_id
            return True
        return False
    
    def get_model_pricing(self, model_id: str) -> tuple[float, float]:
        """Get model pricing (fake pricing for display)"""
        if model_id in self.models:
            model = self.models[model_id]
            return model.input_cost_per_million, model.output_cost_per_million
        return 0.14, 0.28  # Default to DeepSeek pricing
    
    def calculate_fake_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Calculate fake cost based on current model pricing"""
        model = self.get_current_model()
        input_cost = (input_tokens / 1_000_000) * model.input_cost_per_million
        output_cost = (output_tokens / 1_000_000) * model.output_cost_per_million
        return input_cost + output_cost
    
    def get_model_display_name(self) -> str:
        """Get current model display name"""
        return self.get_current_model().display_name
    
    def create_model_selection_table(self) -> Table:
        """Create a table showing all available models"""
        table = Table(title="🤖 Available AI Models", border_style="border", show_header=True)
        table.add_column("Model", style="primary", width=20)
        table.add_column("Tier", style="text", width=10)
        table.add_column("Speed", style="text", width=10)
        table.add_column("Quality", style="text", width=10)
        table.add_column("Input Cost", style="warning", width=12)
        table.add_column("Output Cost", style="success", width=12)
        table.add_column("Context", style="muted", width=10)
        
        for model_id, model in self.models.items():
            # Highlight current model
            style = "bold" if model_id == self.current_model else ""
            
            # Tier styling
            tier_style = {
                "free": "green",
                "premium": "yellow", 
                "enterprise": "red"
            }.get(model.tier, "white")
            
            # Speed styling
            speed_style = {
                "fast": "green",
                "balanced": "yellow",
                "slow": "red"
            }.get(model.speed, "white")
            
            # Quality styling
            quality_style = {
                "good": "yellow",
                "excellent": "green",
                "superior": "bright_green"
            }.get(model.quality, "white")
            
            model_name = f"{model.display_name}" + (" ⭐" if model_id == self.current_model else "")
            if style:
                model_name = f"[{style}]{model_name}[/{style}]"

            table.add_row(
                model_name,
                f"[{tier_style}]{model.tier.title()}[/{tier_style}]",
                f"[{speed_style}]{model.speed.title()}[/{speed_style}]",
                f"[{quality_style}]{model.quality.title()}[/{quality_style}]",
                f"${model.input_cost_per_million:.3f}/1M",
                f"${model.output_cost_per_million:.2f}/1M",
                f"{model.context_window//1000}K"
            )
        
        return table
    
    def create_model_details_panel(self, model_id: str) -> Panel:
        """Create detailed panel for a specific model"""
        if model_id not in self.models:
            return Panel("Model not found", title="Error", border_style="red")
        
        model = self.models[model_id]
        
        # Create content
        content = []
        content.append(f"[bold]{model.display_name}[/bold]")
        content.append(f"[muted]{model.description}[/muted]")
        content.append("")
        
        # Pricing info
        content.append("[bold]💰 Pricing:[/bold]")
        content.append(f"  Input: ${model.input_cost_per_million:.3f} per 1M tokens")
        content.append(f"  Output: ${model.output_cost_per_million:.2f} per 1M tokens")
        content.append("")
        
        # Specifications
        content.append("[bold]📋 Specifications:[/bold]")
        content.append(f"  Context Window: {model.context_window:,} tokens")
        content.append(f"  Max Output: {model.max_output:,} tokens")
        content.append(f"  Tier: {model.tier.title()}")
        content.append(f"  Speed: {model.speed.title()}")
        content.append(f"  Quality: {model.quality.title()}")
        content.append("")
        
        # Features
        content.append("[bold]✨ Features:[/bold]")
        for feature in model.features:
            content.append(f"  {feature}")
        
        return Panel(
            "\n".join(content),
            title=f"Model Details: {model.display_name}",
            border_style="primary" if model_id == self.current_model else "border",
            padding=(1, 2)
        )
    
    def create_pricing_comparison(self) -> Table:
        """Create pricing comparison table"""
        table = Table(title="💰 Pricing Comparison", border_style="border")
        table.add_column("Model", style="primary")
        table.add_column("Tier", style="text")
        table.add_column("1K Tokens (Input)", style="warning")
        table.add_column("1K Tokens (Output)", style="success")
        table.add_column("10K Conversation", style="muted")
        
        for model_id, model in self.models.items():
            # Calculate example costs
            input_1k = (model.input_cost_per_million / 1000)
            output_1k = (model.output_cost_per_million / 1000)
            conversation_10k = (5000 * model.input_cost_per_million / 1_000_000) + (5000 * model.output_cost_per_million / 1_000_000)
            
            tier_emoji = {
                "free": "🆓",
                "premium": "⭐",
                "enterprise": "💎"
            }.get(model.tier, "")
            
            table.add_row(
                model.display_name,
                f"{tier_emoji} {model.tier.title()}",
                f"${input_1k:.4f}",
                f"${output_1k:.4f}",
                f"${conversation_10k:.4f}"
            )
        
        return table
