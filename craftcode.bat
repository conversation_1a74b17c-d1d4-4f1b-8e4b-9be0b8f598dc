@echo off
REM CraftCode v1.6 - Windows Launcher
REM Single command to start the AI terminal application

title CraftCode v1.6

echo.
echo 🚀 CraftCode v1.6
echo    Powered by OpenRouter API with DeepSeek model
echo    CraftCode Team
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Error: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    pause
    exit /b 1
)

REM Check if we're in the right directory
if not exist "codecraft\main.py" (
    if not exist "craftcode.py" (
        echo ❌ Error: CraftCode application files not found
        echo Please ensure you're in the correct directory
        pause
        exit /b 1
    )
)

REM Launch the application
if exist "craftcode.py" (
    python craftcode.py
) else (
    python -m codecraft.main
)

if errorlevel 1 (
    echo.
    echo ❌ Application encountered an error
    pause
)

echo.
echo 👋 Thank you for using CraftCode v1.6!
pause
