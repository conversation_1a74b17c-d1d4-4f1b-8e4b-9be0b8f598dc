"""
Token counting and cost management system
"""

import json
import os
from datetime import datetime
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
import tiktoken


@dataclass
class TokenUsage:
    """Token usage information for a single interaction"""
    timestamp: str
    input_tokens: int
    output_tokens: int
    total_tokens: int
    cost: float
    message_preview: str


@dataclass
class SessionStats:
    """Session statistics"""
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    total_tokens: int = 0
    total_cost: float = 0.0
    message_count: int = 0
    session_start: str = ""
    interactions: List[TokenUsage] = None
    
    def __post_init__(self):
        if self.interactions is None:
            self.interactions = []
        if not self.session_start:
            self.session_start = datetime.now().isoformat()


class TokenManager:
    """Manages token counting and cost tracking"""
    
    def __init__(self, model_name: str = "deepseek/deepseek-r1:free"):
        self.model_name = model_name
        self.session_stats = SessionStats()
        self.history_file = "token_history.json"
        
        # DeepSeek pricing (per 1M tokens)
        self.input_cost_per_million = 0.14  # $0.14 per 1M input tokens
        self.output_cost_per_million = 0.28  # $0.28 per 1M output tokens
        
        # Try to initialize tiktoken for more accurate counting
        try:
            # Use cl100k_base encoding as a reasonable approximation
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
            self.has_tiktoken = True
        except Exception:
            self.tokenizer = None
            self.has_tiktoken = False
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text"""
        if self.has_tiktoken and self.tokenizer:
            try:
                return len(self.tokenizer.encode(text))
            except Exception:
                pass
        
        # Fallback: rough estimation (4 characters per token)
        return max(1, len(text) // 4)
    
    def estimate_input_tokens(self, message: str, conversation_history: List[Dict[str, str]]) -> int:
        """Estimate input tokens for a message including conversation context"""
        total_text = ""
        
        # Add conversation history
        for msg in conversation_history:
            total_text += f"{msg['role']}: {msg['content']}\n"
        
        # Add current message
        total_text += f"user: {message}\n"
        
        return self.count_tokens(total_text)
    
    def calculate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Calculate cost for given token counts"""
        input_cost = (input_tokens / 1_000_000) * self.input_cost_per_million
        output_cost = (output_tokens / 1_000_000) * self.output_cost_per_million
        return input_cost + output_cost
    
    def add_interaction(self, input_tokens: int, output_tokens: int, cost: float, message_preview: str):
        """Add a new interaction to the session stats"""
        total_tokens = input_tokens + output_tokens
        
        # Create token usage record
        usage = TokenUsage(
            timestamp=datetime.now().isoformat(),
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            total_tokens=total_tokens,
            cost=cost,
            message_preview=message_preview[:100] + "..." if len(message_preview) > 100 else message_preview
        )
        
        # Update session stats
        self.session_stats.total_input_tokens += input_tokens
        self.session_stats.total_output_tokens += output_tokens
        self.session_stats.total_tokens += total_tokens
        self.session_stats.total_cost += cost
        self.session_stats.message_count += 1
        self.session_stats.interactions.append(usage)
    
    def get_session_summary(self) -> Dict[str, any]:
        """Get current session summary"""
        if self.session_stats.message_count == 0:
            return {
                "message_count": 0,
                "total_tokens": 0,
                "total_cost": 0.0,
                "average_tokens_per_message": 0,
                "average_cost_per_message": 0.0
            }
        
        return {
            "message_count": self.session_stats.message_count,
            "total_input_tokens": self.session_stats.total_input_tokens,
            "total_output_tokens": self.session_stats.total_output_tokens,
            "total_tokens": self.session_stats.total_tokens,
            "total_cost": self.session_stats.total_cost,
            "average_tokens_per_message": self.session_stats.total_tokens / self.session_stats.message_count,
            "average_cost_per_message": self.session_stats.total_cost / self.session_stats.message_count,
            "session_start": self.session_stats.session_start,
            "input_cost_ratio": (self.session_stats.total_input_tokens * self.input_cost_per_million / 1_000_000) / max(self.session_stats.total_cost, 0.000001),
            "output_cost_ratio": (self.session_stats.total_output_tokens * self.output_cost_per_million / 1_000_000) / max(self.session_stats.total_cost, 0.000001)
        }
    
    def get_recent_interactions(self, count: int = 10) -> List[TokenUsage]:
        """Get recent interactions"""
        return self.session_stats.interactions[-count:]
    
    def save_session_history(self):
        """Save session history to file"""
        try:
            # Load existing history
            history = []
            if os.path.exists(self.history_file):
                try:
                    with open(self.history_file, 'r', encoding='utf-8') as f:
                        history = json.load(f)
                except Exception:
                    history = []
            
            # Add current session
            session_data = asdict(self.session_stats)
            session_data['session_end'] = datetime.now().isoformat()
            history.append(session_data)
            
            # Keep only last 50 sessions
            history = history[-50:]
            
            # Save updated history
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            print(f"Warning: Could not save token history: {e}")
    
    def load_total_usage(self) -> Dict[str, float]:
        """Load total usage across all sessions"""
        try:
            if not os.path.exists(self.history_file):
                return {"total_tokens": 0, "total_cost": 0.0, "total_sessions": 0}
            
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            
            total_tokens = sum(session.get('total_tokens', 0) for session in history)
            total_cost = sum(session.get('total_cost', 0.0) for session in history)
            total_sessions = len(history)
            
            return {
                "total_tokens": total_tokens,
                "total_cost": total_cost,
                "total_sessions": total_sessions
            }
            
        except Exception:
            return {"total_tokens": 0, "total_cost": 0.0, "total_sessions": 0}
    
    def reset_session(self):
        """Reset current session stats"""
        self.session_stats = SessionStats()
    
    def format_cost(self, cost: float) -> str:
        """Format cost for display"""
        if cost < 0.000001:
            return "$0.000000"
        elif cost < 0.01:
            return f"${cost:.6f}"
        else:
            return f"${cost:.4f}"
    
    def format_tokens(self, tokens: int) -> str:
        """Format token count for display"""
        if tokens >= 1_000_000:
            return f"{tokens/1_000_000:.1f}M"
        elif tokens >= 1_000:
            return f"{tokens/1_000:.1f}K"
        else:
            return str(tokens)
