"""
Slash command system for CraftCode v1.6
"""

import re
from typing import List, Dict, Optional, Callable, Any
from dataclasses import dataclass
from rich.table import Table
from rich.text import Text


@dataclass
class SlashCommand:
    """Represents a slash command"""
    name: str
    description: str
    aliases: List[str]
    handler: Callable
    usage: str = ""
    category: str = "General"


class SlashCommandManager:
    """Manages slash commands and provides autocomplete functionality"""
    
    def __init__(self, terminal):
        self.terminal = terminal
        self.commands: Dict[str, SlashCommand] = {}
        self._register_default_commands()
    
    def _register_default_commands(self):
        """Register default slash commands"""
        commands = [
            SlashCommand(
                name="help",
                description="Show available commands and usage information",
                aliases=["h", "?"],
                handler=self._handle_help,
                usage="/help [command]",
                category="General"
            ),
            SlashCommand(
                name="clear",
                description="Clear the terminal screen",
                aliases=["cls", "c"],
                handler=self._handle_clear,
                usage="/clear",
                category="General"
            ),
            SlashCommand(
                name="history",
                description="Show conversation history",
                aliases=["hist", "h"],
                handler=self._handle_history,
                usage="/history [count]",
                category="Chat Management"
            ),
            SlashCommand(
                name="stats",
                description="Show token usage and cost statistics",
                aliases=["statistics", "usage"],
                handler=self._handle_stats,
                usage="/stats [detailed]",
                category="Analytics"
            ),
            SlashCommand(
                name="save",
                description="Save current conversation to file",
                aliases=["export"],
                handler=self._handle_save,
                usage="/save [filename]",
                category="Chat Management"
            ),
            SlashCommand(
                name="load",
                description="Load a saved conversation",
                aliases=["import", "open"],
                handler=self._handle_load,
                usage="/load [filename]",
                category="Chat Management"
            ),
            SlashCommand(
                name="search",
                description="Search conversation history",
                aliases=["find", "grep"],
                handler=self._handle_search,
                usage="/search <query>",
                category="Chat Management"
            ),
            SlashCommand(
                name="reset",
                description="Clear conversation history",
                aliases=["new", "restart"],
                handler=self._handle_reset,
                usage="/reset",
                category="Chat Management"
            ),
            SlashCommand(
                name="export",
                description="Export conversation to different formats",
                aliases=["download"],
                handler=self._handle_export,
                usage="/export <format> [filename]",
                category="Chat Management"
            ),
            SlashCommand(
                name="analyze",
                description="Analyze conversation patterns and usage",
                aliases=["analysis"],
                handler=self._handle_analyze,
                usage="/analyze [type]",
                category="Analytics"
            ),
            SlashCommand(
                name="cost",
                description="Show detailed cost breakdown",
                aliases=["price", "billing"],
                handler=self._handle_cost,
                usage="/cost [period]",
                category="Analytics"
            ),
            SlashCommand(
                name="models",
                description="Show available AI models",
                aliases=["model", "m"],
                handler=self._handle_models,
                usage="/models [list|switch|pricing]",
                category="Models"
            ),
            SlashCommand(
                name="switch",
                description="Switch to a different AI model",
                aliases=["use"],
                handler=self._handle_switch,
                usage="/switch <model_name>",
                category="Models"
            ),
            SlashCommand(
                name="pricing",
                description="Show model pricing comparison",
                aliases=["prices", "cost"],
                handler=self._handle_pricing,
                usage="/pricing",
                category="Models"
            ),
            SlashCommand(
                name="exit",
                description="Exit the application",
                aliases=["quit", "q"],
                handler=self._handle_exit,
                usage="/exit",
                category="General"
            )
        ]
        
        for cmd in commands:
            self.register_command(cmd)
    
    def register_command(self, command: SlashCommand):
        """Register a new slash command"""
        self.commands[command.name] = command
        
        # Register aliases
        for alias in command.aliases:
            self.commands[alias] = command
    
    def is_slash_command(self, text: str) -> bool:
        """Check if text is a slash command"""
        return text.strip().startswith('/')
    
    def parse_command(self, text: str) -> tuple[str, List[str]]:
        """Parse slash command and return command name and arguments"""
        text = text.strip()
        if not text.startswith('/'):
            return "", []
        
        # Remove the leading slash
        text = text[1:]
        
        # Split into command and arguments
        parts = text.split()
        command = parts[0].lower() if parts else ""
        args = parts[1:] if len(parts) > 1 else []
        
        return command, args
    
    def execute_command(self, text: str) -> bool:
        """Execute a slash command"""
        command_name, args = self.parse_command(text)
        
        if command_name in self.commands:
            try:
                self.commands[command_name].handler(args)
                return True
            except Exception as e:
                self.terminal.ui.print_error(f"Error executing command: {str(e)}")
                return True
        else:
            self.terminal.ui.print_error(f"Unknown command: /{command_name}")
            self._suggest_similar_commands(command_name)
            return True
    
    def get_suggestions(self, partial_command: str) -> List[str]:
        """Get command suggestions for autocomplete"""
        if not partial_command.startswith('/'):
            return []
        
        partial = partial_command[1:].lower()
        suggestions = []
        
        for cmd_name, cmd in self.commands.items():
            if cmd_name.startswith(partial) and cmd_name == cmd.name:  # Only show primary names
                suggestions.append(f"/{cmd_name}")
        
        return sorted(suggestions)
    
    def _suggest_similar_commands(self, command_name: str):
        """Suggest similar commands for typos"""
        suggestions = []
        for cmd_name, cmd in self.commands.items():
            if cmd_name == cmd.name:  # Only check primary names
                # Simple similarity check
                if (command_name in cmd_name or 
                    cmd_name in command_name or 
                    abs(len(command_name) - len(cmd_name)) <= 2):
                    suggestions.append(f"/{cmd_name}")
        
        if suggestions:
            self.terminal.ui.console.print(f"Did you mean: {', '.join(suggestions[:3])}", style="muted")
    
    # Command handlers
    def _handle_help(self, args: List[str]):
        """Handle /help command"""
        if args and args[0] in self.commands:
            # Show specific command help
            cmd = self.commands[args[0]]
            self.terminal.ui.console.print(f"\n[bold primary]/{cmd.name}[/bold primary]")
            self.terminal.ui.console.print(f"Description: {cmd.description}")
            self.terminal.ui.console.print(f"Usage: {cmd.usage}")
            if cmd.aliases:
                self.terminal.ui.console.print(f"Aliases: {', '.join([f'/{alias}' for alias in cmd.aliases])}")
            self.terminal.ui.console.print()
        else:
            # Show all commands grouped by category
            categories = {}
            for cmd_name, cmd in self.commands.items():
                if cmd_name == cmd.name:  # Only show primary names
                    if cmd.category not in categories:
                        categories[cmd.category] = []
                    categories[cmd.category].append(cmd)
            
            self.terminal.ui.console.print("\n[bold]📖 Available Slash Commands[/bold]")
            
            for category, cmds in categories.items():
                table = Table(title=f"{category} Commands", border_style="border", show_header=False)
                table.add_column("Command", style="primary", width=20)
                table.add_column("Description", style="text")
                
                for cmd in sorted(cmds, key=lambda x: x.name):
                    table.add_row(f"/{cmd.name}", cmd.description)
                
                self.terminal.ui.console.print(table)
                self.terminal.ui.console.print()
            
            self.terminal.ui.console.print("[muted]💡 Tip: Use '/help <command>' for detailed information about a specific command[/muted]")
            self.terminal.ui.console.print("[muted]💡 Traditional commands (without /) also work: help, clear, stats, etc.[/muted]")
            self.terminal.ui.console.print()
    
    def _handle_clear(self, args: List[str]):
        """Handle /clear command"""
        self.terminal.ui.clear_screen()
    
    def _handle_history(self, args: List[str]):
        """Handle /history command"""
        count = 10  # default
        if args:
            try:
                count = int(args[0])
            except ValueError:
                self.terminal.ui.print_warning("Invalid count. Using default of 10.")
        
        self.terminal._show_conversation_history(count)
    
    def _handle_stats(self, args: List[str]):
        """Handle /stats command"""
        detailed = args and args[0].lower() in ['detailed', 'detail', 'd']
        self.terminal._show_statistics(detailed)
    
    def _handle_save(self, args: List[str]):
        """Handle /save command"""
        filename = args[0] if args else None
        self.terminal._save_conversation(filename)
    
    def _handle_load(self, args: List[str]):
        """Handle /load command"""
        filename = args[0] if args else None
        self.terminal._load_conversation(filename)
    
    def _handle_search(self, args: List[str]):
        """Handle /search command"""
        if not args:
            self.terminal.ui.print_warning("Please provide a search query. Usage: /search <query>")
            return
        
        query = " ".join(args)
        self.terminal._search_conversations(query)
    
    def _handle_reset(self, args: List[str]):
        """Handle /reset command"""
        self.terminal._reset_conversation()
    
    def _handle_export(self, args: List[str]):
        """Handle /export command"""
        if not args:
            self.terminal.ui.print_warning("Please specify format. Usage: /export <format> [filename]")
            self.terminal.ui.console.print("Available formats: txt, md, json, csv")
            return
        
        format_type = args[0].lower()
        filename = args[1] if len(args) > 1 else None
        self.terminal._export_conversation(format_type, filename)
    
    def _handle_analyze(self, args: List[str]):
        """Handle /analyze command"""
        analysis_type = args[0] if args else "general"
        self.terminal._analyze_conversation(analysis_type)
    
    def _handle_cost(self, args: List[str]):
        """Handle /cost command"""
        period = args[0] if args else "session"
        self.terminal._show_cost_breakdown(period)
    
    def _handle_models(self, args: List[str]):
        """Handle /models command"""
        action = args[0] if args else "list"

        if action == "list":
            # Show model selection table
            table = self.terminal.model_manager.create_model_selection_table()
            self.terminal.ui.console.print(table)

            # Show current model info
            current_model = self.terminal.model_manager.get_current_model()
            self.terminal.ui.console.print(f"\n[bold]Current Model:[/bold] {current_model.display_name}")
            self.terminal.ui.console.print(f"[muted]Use '/switch <model>' to change models[/muted]")

        elif action == "pricing":
            self._handle_pricing([])

        elif action == "switch":
            if len(args) > 1:
                self._handle_switch(args[1:])
            else:
                self.terminal.ui.print_warning("Please specify a model to switch to. Use '/models list' to see available models.")

        self.terminal.ui.console.print()

    def _handle_switch(self, args: List[str]):
        """Handle /switch command"""
        if not args:
            self.terminal.ui.print_warning("Please specify a model to switch to.")
            self.terminal.ui.console.print("Available models: gemini, gpt4o, claude4, deepseek")
            return

        model_name = args[0].lower()

        # Map common names to model IDs
        model_mapping = {
            "gemini": "gemini",
            "gemini-2.5": "gemini",
            "gemini-flash": "gemini",
            "gpt4o": "gpt4o",
            "gpt-4o": "gpt4o",
            "gpt4": "gpt4o",
            "claude": "claude4",
            "claude4": "claude4",
            "claude-4": "claude4",
            "sonnet": "claude4",
            "deepseek": "deepseek",
            "deepseek-r1": "deepseek"
        }

        model_id = model_mapping.get(model_name)
        if not model_id:
            self.terminal.ui.print_error(f"Unknown model: {model_name}")
            self.terminal.ui.console.print("Available models: gemini, gpt4o, claude4, deepseek")
            return

        # Switch model
        if self.terminal.model_manager.switch_model(model_id):
            model = self.terminal.model_manager.get_current_model()
            self.terminal.ui.print_success(f"Switched to {model.display_name}")

            # Show model details
            details_panel = self.terminal.model_manager.create_model_details_panel(model_id)
            self.terminal.ui.console.print(details_panel)
        else:
            self.terminal.ui.print_error("Failed to switch model")

    def _handle_pricing(self, args: List[str]):
        """Handle /pricing command"""
        pricing_table = self.terminal.model_manager.create_pricing_comparison()
        self.terminal.ui.console.print(pricing_table)

        self.terminal.ui.console.print("\n[bold]💡 Pricing Notes:[/bold]")
        self.terminal.ui.console.print("• Prices shown are per 1M tokens")
        self.terminal.ui.console.print("• Actual usage may vary based on conversation complexity")
        self.terminal.ui.console.print("• Free tier models have usage limits")
        self.terminal.ui.console.print("• Enterprise models offer priority access and support")
        self.terminal.ui.console.print()

    def _handle_exit(self, args: List[str]):
        """Handle /exit command"""
        self.terminal.running = False
