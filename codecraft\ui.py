"""
UI components and styling for Claude-inspired terminal interface - CraftCode v1.6
"""

import os
import sys
from typing import Optional, List, Dict, Any
from rich.console import Console
from rich.theme import Theme
from rich.text import Text
from rich.panel import Panel
from rich.columns import Columns
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax
from rich.markdown import Markdown
from rich.rule import Rule
from colorama import init, Fore, Back, Style
from prompt_toolkit import prompt
from prompt_toolkit.shortcuts import confirm
import re

# Try to import tkinter for file dialogs, but handle gracefully if not available
try:
    import tkinter as tk
    from tkinter import filedialog
    TKINTER_AVAILABLE = True
except ImportError:
    TKINTER_AVAILABLE = False

try:
    from .streaming import EnhancedResponseFormatter
except ImportError:
    from streaming import EnhancedResponseFormatter

# Initialize colorama for Windows compatibility
init(autoreset=True)

# Claude-inspired color theme
CLAUDE_THEME = Theme({
    "primary": "#FF6B35",      # Orange accent
    "secondary": "#4A90E2",    # Blue
    "success": "#7ED321",      # Green
    "warning": "#F5A623",      # Yellow
    "error": "#D0021B",        # Red
    "muted": "#9B9B9B",        # Gray
    "background": "#1A1A1A",   # Dark background
    "text": "#FFFFFF",         # White text
    "code_bg": "#2D2D2D",      # Code background
    "border": "#404040",       # Border color
})

class ClaudeUI:
    """Claude-inspired terminal UI manager"""
    
    def __init__(self):
        self.console = Console(theme=CLAUDE_THEME, width=120)
        self.response_formatter = EnhancedResponseFormatter(self.console)
        self.setup_terminal()
    
    def setup_terminal(self):
        """Setup terminal for optimal display"""
        # Clear screen and set title
        os.system('cls' if os.name == 'nt' else 'clear')
        if os.name == 'nt':
            os.system('title CodeCraft AI CLI')
    
    def print_header(self):
        """Print the application header"""
        header_text = Text()
        header_text.append("🚀 ", style="primary")
        header_text.append("CraftCode", style="bold primary")
        header_text.append(" v1.6", style="muted")

        subtitle = Text("Powered by OpenRouter API with DeepSeek model", style="muted")

        panel = Panel(
            Text.assemble(header_text, "\n", subtitle),
            border_style="border",
            padding=(1, 2)
        )

        self.console.print(panel)
        self.console.print()
    
    def print_welcome_message(self):
        """Print welcome message with instructions"""
        welcome_text = [
            "Welcome to your AI-powered terminal! 🎉",
            "",
            "• Type your message and press Enter to chat with AI",
            "• Use '/help' or 'help' to see available commands",
            "• Use slash commands like '/stats', '/history', '/clear'",
            "• Press Ctrl+C or type 'exit' to quit",
            ""
        ]

        for line in welcome_text:
            if line.startswith("•"):
                self.console.print(line, style="secondary")
            else:
                self.console.print(line, style="text")

        self.console.print()
    
    def print_prompt(self, token_count: Optional[int] = None, cost: Optional[float] = None):
        """Print the input prompt with optional token/cost info"""
        prompt_text = Text()
        prompt_text.append("You", style="bold primary")
        
        if token_count is not None and cost is not None:
            prompt_text.append(f" [{token_count} tokens, ${cost:.6f}]", style="muted")
        
        prompt_text.append("> ", style="primary")
        
        self.console.print(prompt_text, end="")
    
    def print_ai_response(self, response: str, input_tokens: int, output_tokens: int, cost: float, use_status_indicators: bool = True):
        """Print AI response with status indicators or instant display"""
        if use_status_indicators:
            # Use status indicators
            self.response_formatter.display_response_with_typing(
                response, input_tokens, output_tokens, cost
            )
        else:
            # Fallback to instant display
            self.response_formatter.display_response_instant(
                response, input_tokens, output_tokens, cost
            )

        # Check for code blocks and offer to save them
        self._check_and_offer_code_saving(response)
    
    def format_response(self, response: str):
        """Format AI response with code highlighting and markdown"""
        # Split response into code blocks and text
        parts = self.split_code_blocks(response)
        
        for part in parts:
            if part['type'] == 'code':
                self.print_code_block(part['content'], part.get('language', 'text'))
            else:
                self.print_text_block(part['content'])
    
    def split_code_blocks(self, text: str) -> List[Dict[str, str]]:
        """Split text into code blocks and regular text"""
        parts = []
        current_pos = 0
        
        # Pattern to match code blocks
        code_pattern = r'```(\w+)?\n(.*?)\n```'
        
        for match in re.finditer(code_pattern, text, re.DOTALL):
            # Add text before code block
            if match.start() > current_pos:
                text_content = text[current_pos:match.start()].strip()
                if text_content:
                    parts.append({'type': 'text', 'content': text_content})
            
            # Add code block
            language = match.group(1) or 'text'
            code_content = match.group(2)
            parts.append({
                'type': 'code',
                'content': code_content,
                'language': language
            })
            
            current_pos = match.end()
        
        # Add remaining text
        if current_pos < len(text):
            remaining_text = text[current_pos:].strip()
            if remaining_text:
                parts.append({'type': 'text', 'content': remaining_text})
        
        # If no code blocks found, treat entire text as text
        if not parts:
            parts.append({'type': 'text', 'content': text})
        
        return parts
    
    def print_code_block(self, code: str, language: str):
        """Print syntax-highlighted code block"""
        try:
            syntax = Syntax(
                code,
                language,
                theme="monokai",
                background_color="default",
                line_numbers=True,
                word_wrap=True
            )
            
            panel = Panel(
                syntax,
                border_style="code_bg",
                title=f"[bold]{language}[/bold]",
                title_align="left"
            )
            
            self.console.print(panel)
        except Exception:
            # Fallback to plain text if syntax highlighting fails
            self.console.print(Panel(code, border_style="muted"))
    
    def print_text_block(self, text: str):
        """Print formatted text block"""
        # Try to render as markdown for better formatting
        try:
            markdown = Markdown(text)
            self.console.print(markdown)
        except Exception:
            # Fallback to plain text
            self.console.print(text, style="text")
    
    def print_error(self, message: str, suggestion: Optional[str] = None):
        """Print error message"""
        error_text = Text()
        error_text.append("❌ Error: ", style="bold error")
        error_text.append(message, style="error")
        
        self.console.print(error_text)
        
        if suggestion:
            self.console.print(f"💡 Suggestion: {suggestion}", style="warning")
        
        self.console.print()
    
    def print_warning(self, message: str):
        """Print warning message"""
        warning_text = Text()
        warning_text.append("⚠️  Warning: ", style="bold warning")
        warning_text.append(message, style="warning")
        
        self.console.print(warning_text)
        self.console.print()
    
    def print_success(self, message: str):
        """Print success message"""
        success_text = Text()
        success_text.append("✅ ", style="success")
        success_text.append(message, style="success")
        
        self.console.print(success_text)
        self.console.print()
    
    def print_info(self, message: str):
        """Print info message"""
        info_text = Text()
        info_text.append("ℹ️  ", style="secondary")
        info_text.append(message, style="text")
        
        self.console.print(info_text)
        self.console.print()
    
    def show_thinking(self) -> Progress:
        """Show thinking animation"""
        progress = Progress(
            SpinnerColumn(),
            TextColumn("[bold secondary]🤖 AI is preparing response..."),
            console=self.console,
            transient=True
        )

        task = progress.add_task("thinking", total=None)
        progress.start()

        return progress
    
    def clear_screen(self):
        """Clear the terminal screen"""
        self.setup_terminal()
        self.print_header()
    
    def show_slash_command_menu(self, model_manager):
        """Show interactive slash command menu"""
        self.console.print()
        self.console.print("🔧 [bold primary]Slash Command Menu[/bold primary]", style="primary")
        self.console.print("Type the number or command name to execute:", style="muted")
        self.console.print()

        # Create menu table
        menu_table = Table(border_style="border", show_header=True)
        menu_table.add_column("#", style="primary", width=3)
        menu_table.add_column("Command", style="secondary", width=15)
        menu_table.add_column("Description", style="text", width=40)
        menu_table.add_column("Category", style="muted", width=15)

        menu_items = [
            ("1", "/models", "Show available AI models", "Models"),
            ("2", "/switch", "Switch AI model", "Models"),
            ("3", "/pricing", "Show model pricing", "Models"),
            ("4", "/help", "Show all commands", "General"),
            ("5", "/clear", "Clear screen", "General"),
            ("6", "/history", "Show conversation history", "Chat"),
            ("7", "/stats", "Show usage statistics", "Chat"),
            ("8", "/save", "Save conversation", "Chat"),
            ("9", "/reset", "Reset conversation", "Chat"),
            ("0", "/exit", "Exit application", "General")
        ]

        for num, cmd, desc, cat in menu_items:
            menu_table.add_row(num, cmd, desc, cat)

        self.console.print(menu_table)
        self.console.print()

        # Show current model info
        current_model = model_manager.get_current_model()
        model_info = Text()
        model_info.append("Current Model: ", style="muted")
        model_info.append(f"{current_model.display_name}", style="bold primary")
        model_info.append(f" ({current_model.tier.title()} Tier)", style="muted")
        self.console.print(model_info)
        self.console.print()

        self.console.print("💡 Tip: Type '/' anytime to see this menu", style="muted")
        self.console.print()

    def print_help(self):
        """Print help information"""
        help_table = Table(title="Available Commands", border_style="border")
        help_table.add_column("Command", style="primary", width=15)
        help_table.add_column("Description", style="text")

        commands = [
            ("help", "Show this help message"),
            ("clear", "Clear the terminal screen"),
            ("history", "Show conversation history"),
            ("stats", "Show token usage and cost statistics"),
            ("save", "Save current conversation to file"),
            ("load", "Load a saved conversation"),
            ("reset", "Clear conversation history"),
            ("exit/quit", "Exit the application"),
        ]

        for cmd, desc in commands:
            help_table.add_row(cmd, desc)

        self.console.print(help_table)
        self.console.print()
        self.console.print("💡 Tip: Type '/' to see the interactive slash command menu.", style="muted")
        self.console.print()

    def _check_and_offer_code_saving(self, response: str):
        """Check if response contains code blocks and offer to save them"""
        code_blocks = self._extract_code_blocks(response)

        if code_blocks:
            self.console.print()
            self.console.print("💾 [bold primary]Code detected in response![/bold primary]", style="primary")

            for i, block in enumerate(code_blocks, 1):
                language = block.get('language', 'text')
                code = block['content']

                # Show preview of code block
                preview = code[:100] + "..." if len(code) > 100 else code
                self.console.print(f"[bold]Block {i}[/bold] ({language}): {preview}", style="muted")

            self.console.print()

            try:
                # Ask if user wants to save code
                save_code = confirm("Would you like to save the generated code?", default=False)

                if save_code:
                    self._save_code_blocks(code_blocks)

            except (KeyboardInterrupt, EOFError):
                self.console.print("Code saving cancelled.", style="muted")

    def _extract_code_blocks(self, text: str) -> List[Dict[str, str]]:
        """Extract code blocks from text"""
        code_blocks = []

        # Pattern to match code blocks
        code_pattern = r'```(\w+)?\n(.*?)\n```'

        for match in re.finditer(code_pattern, text, re.DOTALL):
            language = match.group(1) or 'text'
            code_content = match.group(2).strip()

            if code_content:  # Only include non-empty code blocks
                code_blocks.append({
                    'language': language,
                    'content': code_content
                })

        return code_blocks

    def _save_code_blocks(self, code_blocks: List[Dict[str, str]]):
        """Save code blocks to files"""
        for i, block in enumerate(code_blocks, 1):
            language = block['language']
            code = block['content']

            try:
                # Determine file extension
                extension = self._get_file_extension(language)

                if len(code_blocks) == 1:
                    filename = f"generated_code{extension}"
                else:
                    filename = f"generated_code_{i}{extension}"

                # Try to use file dialog first, fallback to prompt
                filepath = self._get_save_path(filename)

                if filepath:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(code)

                    self.print_success(f"Code saved to: {os.path.basename(filepath)}")
                else:
                    self.console.print("Save cancelled.", style="muted")

            except Exception as e:
                self.print_error(f"Failed to save code block {i}: {str(e)}")

    def _get_file_extension(self, language: str) -> str:
        """Get appropriate file extension for language"""
        extensions = {
            'python': '.py',
            'javascript': '.js',
            'typescript': '.ts',
            'java': '.java',
            'cpp': '.cpp',
            'c': '.c',
            'csharp': '.cs',
            'php': '.php',
            'ruby': '.rb',
            'go': '.go',
            'rust': '.rs',
            'swift': '.swift',
            'kotlin': '.kt',
            'scala': '.scala',
            'html': '.html',
            'css': '.css',
            'sql': '.sql',
            'bash': '.sh',
            'powershell': '.ps1',
            'json': '.json',
            'xml': '.xml',
            'yaml': '.yml',
            'markdown': '.md',
            'dockerfile': '.dockerfile'
        }

        return extensions.get(language.lower(), '.txt')

    def _get_save_path(self, default_filename: str) -> Optional[str]:
        """Get save path using file dialog or prompt"""
        if TKINTER_AVAILABLE:
            try:
                # Try to use tkinter file dialog
                root = tk.Tk()
                root.withdraw()  # Hide the main window
                root.attributes('-topmost', True)  # Bring to front

                filepath = filedialog.asksaveasfilename(
                    title="Save Code File",
                    defaultextension=os.path.splitext(default_filename)[1],
                    initialname=default_filename,
                    filetypes=[
                        ("All Files", "*.*"),
                        ("Python Files", "*.py"),
                        ("JavaScript Files", "*.js"),
                        ("Text Files", "*.txt"),
                        ("HTML Files", "*.html"),
                        ("CSS Files", "*.css"),
                        ("JSON Files", "*.json")
                    ]
                )

                root.destroy()
                return filepath if filepath else None

            except Exception:
                pass  # Fall through to prompt-based input

        # Fallback to prompt-based input
        try:
            self.console.print(f"Enter file path (default: {default_filename}):", style="muted")
            user_path = prompt("Save as: ", default=default_filename)

            if user_path.strip():
                # Expand user path and make absolute
                expanded_path = os.path.expanduser(user_path.strip())
                return os.path.abspath(expanded_path)

        except (KeyboardInterrupt, EOFError):
            pass

        return None
