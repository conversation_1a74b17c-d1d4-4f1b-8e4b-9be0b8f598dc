"""
UI components and styling for Claude-inspired terminal interface - CraftCode v1.6
"""

import os
import sys
from typing import Optional, List, Dict, Any
from rich.console import Console
from rich.theme import Theme
from rich.text import Text
from rich.panel import Panel
from rich.columns import Columns
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.syntax import Syntax
from rich.markdown import Markdown
from rich.rule import Rule
from colorama import init, Fore, Back, Style
import re

try:
    from .streaming import EnhancedResponseFormatter
except ImportError:
    from streaming import EnhancedResponseFormatter

# Initialize colorama for Windows compatibility
init(autoreset=True)

# Claude-inspired color theme
CLAUDE_THEME = Theme({
    "primary": "#FF6B35",      # Orange accent
    "secondary": "#4A90E2",    # Blue
    "success": "#7ED321",      # Green
    "warning": "#F5A623",      # Yellow
    "error": "#D0021B",        # Red
    "muted": "#9B9B9B",        # Gray
    "background": "#1A1A1A",   # Dark background
    "text": "#FFFFFF",         # White text
    "code_bg": "#2D2D2D",      # Code background
    "border": "#404040",       # Border color
})

class ClaudeUI:
    """Claude-inspired terminal UI manager"""
    
    def __init__(self):
        self.console = Console(theme=CLAUDE_THEME, width=120)
        self.response_formatter = EnhancedResponseFormatter(self.console)
        self.setup_terminal()
    
    def setup_terminal(self):
        """Setup terminal for optimal display"""
        # Clear screen and set title
        os.system('cls' if os.name == 'nt' else 'clear')
        if os.name == 'nt':
            os.system('title CodeCraft AI CLI')
    
    def print_header(self):
        """Print the application header"""
        header_text = Text()
        header_text.append("🚀 ", style="primary")
        header_text.append("CraftCode", style="bold primary")
        header_text.append(" v1.6", style="muted")

        subtitle = Text("Powered by OpenRouter API with DeepSeek model", style="muted")

        panel = Panel(
            Text.assemble(header_text, "\n", subtitle),
            border_style="border",
            padding=(1, 2)
        )

        self.console.print(panel)
        self.console.print()
    
    def print_welcome_message(self):
        """Print welcome message with instructions"""
        welcome_text = [
            "Welcome to your AI-powered terminal! 🎉",
            "",
            "• Type your message and press Enter to chat with AI",
            "• Use '/help' or 'help' to see available commands",
            "• Use slash commands like '/stats', '/history', '/clear'",
            "• Press Ctrl+C or type 'exit' to quit",
            ""
        ]

        for line in welcome_text:
            if line.startswith("•"):
                self.console.print(line, style="secondary")
            else:
                self.console.print(line, style="text")

        self.console.print()
    
    def print_prompt(self, token_count: Optional[int] = None, cost: Optional[float] = None):
        """Print the input prompt with optional token/cost info"""
        prompt_text = Text()
        prompt_text.append("You", style="bold primary")
        
        if token_count is not None and cost is not None:
            prompt_text.append(f" [{token_count} tokens, ${cost:.6f}]", style="muted")
        
        prompt_text.append("> ", style="primary")
        
        self.console.print(prompt_text, end="")
    
    def print_ai_response(self, response: str, input_tokens: int, output_tokens: int, cost: float, use_typing: bool = True):
        """Print AI response with typing animation or instant display"""
        if use_typing:
            # Use typing animation
            self.response_formatter.display_response_with_typing(
                response, input_tokens, output_tokens, cost
            )
        else:
            # Fallback to instant display
            self.response_formatter.display_response_instant(
                response, input_tokens, output_tokens, cost
            )
    
    def format_response(self, response: str):
        """Format AI response with code highlighting and markdown"""
        # Split response into code blocks and text
        parts = self.split_code_blocks(response)
        
        for part in parts:
            if part['type'] == 'code':
                self.print_code_block(part['content'], part.get('language', 'text'))
            else:
                self.print_text_block(part['content'])
    
    def split_code_blocks(self, text: str) -> List[Dict[str, str]]:
        """Split text into code blocks and regular text"""
        parts = []
        current_pos = 0
        
        # Pattern to match code blocks
        code_pattern = r'```(\w+)?\n(.*?)\n```'
        
        for match in re.finditer(code_pattern, text, re.DOTALL):
            # Add text before code block
            if match.start() > current_pos:
                text_content = text[current_pos:match.start()].strip()
                if text_content:
                    parts.append({'type': 'text', 'content': text_content})
            
            # Add code block
            language = match.group(1) or 'text'
            code_content = match.group(2)
            parts.append({
                'type': 'code',
                'content': code_content,
                'language': language
            })
            
            current_pos = match.end()
        
        # Add remaining text
        if current_pos < len(text):
            remaining_text = text[current_pos:].strip()
            if remaining_text:
                parts.append({'type': 'text', 'content': remaining_text})
        
        # If no code blocks found, treat entire text as text
        if not parts:
            parts.append({'type': 'text', 'content': text})
        
        return parts
    
    def print_code_block(self, code: str, language: str):
        """Print syntax-highlighted code block"""
        try:
            syntax = Syntax(
                code,
                language,
                theme="monokai",
                background_color="default",
                line_numbers=True,
                word_wrap=True
            )
            
            panel = Panel(
                syntax,
                border_style="code_bg",
                title=f"[bold]{language}[/bold]",
                title_align="left"
            )
            
            self.console.print(panel)
        except Exception:
            # Fallback to plain text if syntax highlighting fails
            self.console.print(Panel(code, border_style="muted"))
    
    def print_text_block(self, text: str):
        """Print formatted text block"""
        # Try to render as markdown for better formatting
        try:
            markdown = Markdown(text)
            self.console.print(markdown)
        except Exception:
            # Fallback to plain text
            self.console.print(text, style="text")
    
    def print_error(self, message: str, suggestion: Optional[str] = None):
        """Print error message"""
        error_text = Text()
        error_text.append("❌ Error: ", style="bold error")
        error_text.append(message, style="error")
        
        self.console.print(error_text)
        
        if suggestion:
            self.console.print(f"💡 Suggestion: {suggestion}", style="warning")
        
        self.console.print()
    
    def print_warning(self, message: str):
        """Print warning message"""
        warning_text = Text()
        warning_text.append("⚠️  Warning: ", style="bold warning")
        warning_text.append(message, style="warning")
        
        self.console.print(warning_text)
        self.console.print()
    
    def print_success(self, message: str):
        """Print success message"""
        success_text = Text()
        success_text.append("✅ ", style="success")
        success_text.append(message, style="success")
        
        self.console.print(success_text)
        self.console.print()
    
    def print_info(self, message: str):
        """Print info message"""
        info_text = Text()
        info_text.append("ℹ️  ", style="secondary")
        info_text.append(message, style="text")
        
        self.console.print(info_text)
        self.console.print()
    
    def show_thinking(self) -> Progress:
        """Show thinking animation"""
        progress = Progress(
            SpinnerColumn(),
            TextColumn("[bold secondary]Claude is thinking..."),
            console=self.console,
            transient=True
        )
        
        task = progress.add_task("thinking", total=None)
        progress.start()
        
        return progress
    
    def clear_screen(self):
        """Clear the terminal screen"""
        self.setup_terminal()
        self.print_header()
    
    def print_help(self):
        """Print help information"""
        help_table = Table(title="Available Commands", border_style="border")
        help_table.add_column("Command", style="primary", width=15)
        help_table.add_column("Description", style="text")
        
        commands = [
            ("help", "Show this help message"),
            ("clear", "Clear the terminal screen"),
            ("history", "Show conversation history"),
            ("stats", "Show token usage and cost statistics"),
            ("save", "Save current conversation to file"),
            ("load", "Load a saved conversation"),
            ("reset", "Clear conversation history"),
            ("exit/quit", "Exit the application"),
        ]
        
        for cmd, desc in commands:
            help_table.add_row(cmd, desc)
        
        self.console.print(help_table)
        self.console.print()
        self.console.print("💡 Tip: Anything else will be sent to the AI model for processing.", style="muted")
        self.console.print()
